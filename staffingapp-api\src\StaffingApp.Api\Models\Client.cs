namespace StaffingApp.Api.Models;

public class Client
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public ClientType ClientType { get; set; }    // Navigation properties
    public ICollection<User> Users { get; set; } = new List<User>();
    public ICollection<Job> Jobs { get; set; } = new List<Job>();

    // Statistics
    public decimal HoursBought { get; set; }
}

public enum ClientType
{
    Individual,
    Corporate,
    Government
}
