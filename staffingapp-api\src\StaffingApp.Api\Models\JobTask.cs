namespace StaffingApp.Api.Models;

public class JobTask
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal HoursSpent { get; set; }
    public DateTime CreatedAt { get; set; }
    public OutputFormat OutputFormat { get; set; }
    public string? ReferenceUrl { get; set; }
    
    // Navigation properties
    public int JobId { get; set; }
    public Job Job { get; set; } = null!;
    
    public ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();
}
