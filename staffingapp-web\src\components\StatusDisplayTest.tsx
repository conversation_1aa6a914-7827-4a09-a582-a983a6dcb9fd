'use client';

import { useState } from 'react';
import { getRoleBasedStatusDisplay, getStatusColors } from '@/utils/statusDisplay';

export default function StatusDisplayTest() {
  const [selectedRole, setSelectedRole] = useState('User');

  const backendStatuses = ['New', 'Returned', 'Assigned', 'InProgress', 'AwaitingReview', 'Released', 'Closed'];
  const roles = ['User', 'Staff', 'Supervisor', 'Admin'];

  // Sample jobs for testing
  const sampleJobs = [
    { id: 1, title: 'Data Entry Task', status: 'New', description: 'Enter customer data' },
    { id: 2, title: 'Document Review', status: 'Assigned', description: 'Review legal documents' },
    { id: 3, title: 'Content Writing', status: 'InProgress', description: 'Write blog post' },
    { id: 4, title: 'Data Analysis', status: 'AwaitingReview', description: 'Analyze sales data' },
    { id: 5, title: 'Report Generation', status: 'Released', description: 'Generate monthly report' },
    { id: 6, title: 'Quality Check', status: 'Closed', description: 'Quality assurance check' },
    { id: 7, title: 'Revision Task', status: 'Returned', description: 'Revise previous work' }
  ];

  const getStatusBadge = (status: string, role: string) => {
    const displayText = getRoleBasedStatusDisplay(status, role);
    const colors = getStatusColors(status);

    return (
      <span style={{
        display: 'inline-flex',
        alignItems: 'center',
        padding: '4px 12px',
        borderRadius: '9999px',
        fontSize: '12px',
        fontWeight: '500',
        backgroundColor: colors.bg,
        color: colors.text,
        border: `1px solid ${colors.border}`,
        marginRight: '8px'
      }}>
        {displayText}
      </span>
    );
  };

  return (
    <div style={{ padding: '20px', backgroundColor: 'white', margin: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
      <h2 style={{ marginBottom: '20px', fontSize: '18px', fontWeight: 'bold' }}>Status Display Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <label style={{ marginRight: '10px', fontWeight: '500' }}>Select Role:</label>
        <select 
          value={selectedRole} 
          onChange={(e) => setSelectedRole(e.target.value)}
          style={{ padding: '4px 8px', border: '1px solid #ccc', borderRadius: '4px' }}
        >
          {roles.map(role => (
            <option key={role} value={role}>{role}</option>
          ))}
        </select>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ marginBottom: '10px', fontSize: '16px', fontWeight: '600' }}>
          Status Display for {selectedRole} Role:
        </h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
          {backendStatuses.map(status => (
            <div key={status} style={{ marginBottom: '8px' }}>
              <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                {status} →
              </div>
              {getStatusBadge(status, selectedRole)}
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '10px', fontSize: '16px', fontWeight: '600' }}>
          All Roles Comparison:
        </h3>
        <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #ddd' }}>
          <thead>
            <tr style={{ backgroundColor: '#f5f5f5' }}>
              <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left' }}>Status</th>
              {roles.map(role => (
                <th key={role} style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left' }}>{role}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {backendStatuses.map(status => (
              <tr key={status}>
                <td style={{ padding: '8px', border: '1px solid #ddd', fontWeight: '500' }}>{status}</td>
                {roles.map(role => (
                  <td key={role} style={{ padding: '8px', border: '1px solid #ddd' }}>
                    {getStatusBadge(status, role)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3 style={{ marginBottom: '10px', fontSize: '16px', fontWeight: '600' }}>
          Sample Jobs as {selectedRole} would see them:
        </h3>
        <div style={{ display: 'grid', gap: '8px' }}>
          {sampleJobs.map(job => (
            <div key={job.id} style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '8px 12px',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              border: '1px solid #dee2e6'
            }}>
              <div>
                <div style={{ fontWeight: '500', fontSize: '14px' }}>{job.title}</div>
                <div style={{ fontSize: '12px', color: '#6c757d' }}>{job.description}</div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '11px', color: '#6c757d' }}>({job.status})</span>
                {getStatusBadge(job.status, selectedRole)}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f9ff', borderRadius: '4px' }}>
        <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '600' }}>Expected Mappings:</h4>
        <ul style={{ fontSize: '12px', margin: 0, paddingLeft: '20px' }}>
          <li><strong>Client (User):</strong> New → Submitted, AwaitingReview → Awaiting Supervisor Review, Released → Completed</li>
          <li><strong>Supervisor:</strong> AwaitingReview → Awaiting My Review</li>
          <li><strong>Staff:</strong> Assigned → Assigned to Me, AwaitingReview → Awaiting Supervisor Review</li>
        </ul>
      </div>
    </div>
  );
}
