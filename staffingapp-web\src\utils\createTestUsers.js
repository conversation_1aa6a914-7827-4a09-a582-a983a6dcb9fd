// Script to create test users for each role
// Run this in browser console or as a Node.js script

const API_BASE = 'http://localhost:5000/api';

async function createTestUsers() {
  console.log('🚀 Creating test users for all roles...');

  try {
    // 1. Create Client User (Individual)
    console.log('\n📝 Creating Client User (Individual)...');
    const clientResponse = await fetch(`${API_BASE}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firstName: 'Test',
        lastName: 'Client',
        email: '<EMAIL>',
        password: 'test123',
        clientType: 'individual',
        companyName: '',
        role: 'User'
      }),
    });

    if (clientResponse.ok) {
      const clientData = await clientResponse.json();
      console.log('✅ Client user created:', clientData);
    } else {
      const error = await clientResponse.text();
      console.log('⚠️ Client user creation failed (may already exist):', error);
    }

    // 2. Create Corporate Client User
    console.log('\n📝 Creating Corporate Client User...');
    const corporateResponse = await fetch(`${API_BASE}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firstName: 'Corporate',
        lastName: 'Client',
        email: '<EMAIL>',
        password: 'test123',
        clientType: 'corporate',
        companyName: 'Test Corporation',
        role: 'User'
      }),
    });

    if (corporateResponse.ok) {
      const corporateData = await corporateResponse.json();
      console.log('✅ Corporate client user created:', corporateData);
    } else {
      const error = await corporateResponse.text();
      console.log('⚠️ Corporate client user creation failed (may already exist):', error);
    }

    // 3. Create Staff User
    console.log('\n📝 Creating Staff User...');
    const staffResponse = await fetch(`${API_BASE}/auth/create-staff`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (staffResponse.ok) {
      const staffData = await staffResponse.json();
      console.log('✅ Staff user created:', staffData);
    } else {
      const error = await staffResponse.text();
      console.log('⚠️ Staff user creation failed (may already exist):', error);
    }

    // 4. Create Supervisor User
    console.log('\n📝 Creating Supervisor User...');
    const supervisorResponse = await fetch(`${API_BASE}/auth/create-supervisor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (supervisorResponse.ok) {
      const supervisorData = await supervisorResponse.json();
      console.log('✅ Supervisor user created:', supervisorData);
    } else {
      const error = await supervisorResponse.text();
      console.log('⚠️ Supervisor user creation failed (may already exist):', error);
    }

    // 5. Seed all role users (includes Admin)
    console.log('\n📝 Seeding all role users (including Admin)...');
    const seedResponse = await fetch(`${API_BASE}/auth/seed-roles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (seedResponse.ok) {
      const seedData = await seedResponse.json();
      console.log('✅ Role users seeded:', seedData);
    } else {
      const error = await seedResponse.text();
      console.log('⚠️ Role seeding failed (may already exist):', error);
    }

    // 6. Get all users to verify
    console.log('\n📋 Getting all users to verify creation...');
    const usersResponse = await fetch(`${API_BASE}/auth/test-users`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (usersResponse.ok) {
      const usersData = await usersResponse.json();
      console.log('📊 All users in system:', usersData);
      
      // Display login credentials
      console.log('\n🔑 LOGIN CREDENTIALS FOR TESTING:');
      console.log('=====================================');
      console.log('👤 CLIENT (Individual): <EMAIL> / test123');
      console.log('🏢 CLIENT (Corporate): <EMAIL> / test123');
      console.log('👷 STAFF: <EMAIL> / staff123');
      console.log('👨‍💼 SUPERVISOR: <EMAIL> / supervisor123');
      console.log('🔧 ADMIN: <EMAIL> / admin123');
      console.log('=====================================');
      console.log('\n📋 TESTING INSTRUCTIONS:');
      console.log('1. Log out of current session');
      console.log('2. Log in with each role using credentials above');
      console.log('3. Check Work History section to see role-based status display');
      console.log('4. Expected status mappings:');
      console.log('   • Client: New→Submitted, AwaitingReview→Awaiting Supervisor Review, Released→Completed');
      console.log('   • Staff: Assigned→Assigned to Me, AwaitingReview→Awaiting Supervisor Review');
      console.log('   • Supervisor: AwaitingReview→Awaiting My Review');
      console.log('   • Admin: Shows system status names');
      console.log('=====================================');
      
    } else {
      const error = await usersResponse.text();
      console.log('❌ Failed to get users:', error);
    }

  } catch (error) {
    console.error('❌ Error creating test users:', error);
  }
}

// Export for use in browser or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  window.createTestUsers = createTestUsers;
  console.log('🌐 Run createTestUsers() in browser console to create test users');
} else {
  // Node.js environment
  module.exports = { createTestUsers };
}

// Auto-run if this script is executed directly
if (typeof window !== 'undefined' && window.location.pathname.includes('dashboard')) {
  console.log('🎯 Dashboard detected. You can run createTestUsers() to create test users.');
}
