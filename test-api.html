<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <div id="results"></div>
    
    <script>
        async function testLogin() {
            try {
                console.log('Testing login...');
                document.getElementById('results').innerHTML = '<p>🔄 Testing login...</p>';

                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password456'
                    }),
                });

                console.log('Login response status:', response.status);
                const data = await response.json();
                console.log('Login response data:', data);

                if (response.ok) {
                    document.getElementById('results').innerHTML += '<p>✅ Login successful</p>';
                    document.getElementById('results').innerHTML += `<p>User: ${data.user.firstName} ${data.user.lastName} (Client ID: ${data.user.clientId})</p>`;

                    // Test job submission endpoint
                    document.getElementById('results').innerHTML += '<p>🔄 Testing job submission API...</p>';
                    const jobData = {
                        title: 'Test Job from API Test',
                        description: 'This is a test job created via API test',
                        jobType: 'DataEntry',
                        category: 'DataProcessing',
                        outputFormat: 'PDF',
                        attachmentUrl: 'https://example.com/test.pdf',
                        referenceUrl: 'https://example.com/reference',
                        clientId: data.user.clientId
                    };

                    const jobResponse = await fetch('http://localhost:5000/api/jobs', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${data.token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(jobData)
                    });

                    console.log('Job response status:', jobResponse.status);

                    if (jobResponse.ok) {
                        const jobResult = await jobResponse.json();
                        console.log('Job response data:', jobResult);
                        document.getElementById('results').innerHTML += '<p>✅ Job submission successful</p>';
                        document.getElementById('results').innerHTML += `<div style="background: #f0fff0; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <h3>📝 Job Created:</h3>
                            <ul>
                                <li><strong>ID:</strong> ${jobResult.id}</li>
                                <li><strong>Title:</strong> ${jobResult.title}</li>
                                <li><strong>Description:</strong> ${jobResult.description}</li>
                                <li><strong>Type:</strong> ${jobResult.jobType}</li>
                                <li><strong>Category:</strong> ${jobResult.category}</li>
                                <li><strong>Client ID:</strong> ${jobResult.clientId}</li>
                            </ul>
                        </div>`;
                    } else {
                        const errorData = await jobResponse.text();
                        console.log('Job error data:', errorData);
                        document.getElementById('results').innerHTML += '<p>❌ Job submission failed</p>';
                        document.getElementById('results').innerHTML += `<pre style="background: #ffe6e6; padding: 10px; border-radius: 5px;">${errorData}</pre>`;
                    }

                    // Test getting jobs list
                    document.getElementById('results').innerHTML += '<p>🔄 Testing jobs list API...</p>';
                    const jobsListResponse = await fetch('http://localhost:5000/api/jobs', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${data.token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log('Jobs list response status:', jobsListResponse.status);

                    if (jobsListResponse.ok) {
                        const jobsList = await jobsListResponse.json();
                        console.log('Jobs list response data:', jobsList);
                        document.getElementById('results').innerHTML += '<p>✅ Jobs list API successful</p>';
                        document.getElementById('results').innerHTML += `<div style="background: #f0fff0; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <h3>📋 Jobs List (${jobsList.length} jobs):</h3>
                            ${jobsList.map(job => `
                                <div style="border: 1px solid #ddd; padding: 8px; margin: 5px 0; border-radius: 3px;">
                                    <strong>ID:</strong> ${job.id}<br>
                                    <strong>Title:</strong> ${job.title}<br>
                                    <strong>Status:</strong> ${job.status}<br>
                                    <strong>Type:</strong> ${job.jobType}<br>
                                    <strong>Category:</strong> ${job.category}<br>
                                    <strong>Created:</strong> ${new Date(job.createdAt).toLocaleString()}
                                </div>
                            `).join('')}
                        </div>`;
                    } else {
                        const errorData = await jobsListResponse.text();
                        console.log('Jobs list error data:', errorData);
                        document.getElementById('results').innerHTML += '<p>❌ Jobs list API failed</p>';
                        document.getElementById('results').innerHTML += `<pre style="background: #ffe6e6; padding: 10px; border-radius: 5px;">${errorData}</pre>`;
                    }
                } else {
                    document.getElementById('results').innerHTML += '<p>❌ Login failed</p>';
                    document.getElementById('results').innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                console.error('Test error:', error);
                document.getElementById('results').innerHTML += `<p>❌ Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testLogin();
    </script>
</body>
</html>
