/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?4c03":
/*!***********************!*\
  !*** debug (ignored) ***!
  \***********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWUlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q3N0YWZmaW5nYXBwJTVDJTVDc3RhZmZpbmdhcHAtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFzSSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWZmaW5nYXBwLXdlYi8/NjVlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhvbWVcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXHN0YWZmaW5nYXBwXFxcXHN0YWZmaW5nYXBwLXdlYlxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api_statistics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/statistics */ \"(ssr)/./src/lib/api/statistics.ts\");\n/* harmony import */ var _components_JobList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/JobList */ \"(ssr)/./src/components/JobList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hoursBought: 0,\n        hoursUtilized: 0,\n        hoursAvailable: 0\n    });\n    const [statement, setStatement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statementLoading, setStatementLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchStatistics = async ()=>{\n            try {\n                // Check if we're in the browser before accessing sessionStorage\n                if (true) {\n                    setIsLoading(false);\n                    return;\n                }\n                const token = sessionStorage.getItem(\"token\");\n                const userStr = sessionStorage.getItem(\"user\");\n                console.log(\"Dashboard: Checking authentication...\", {\n                    hasToken: !!token,\n                    hasUser: !!userStr\n                });\n                if (!token || !userStr) {\n                    console.log(\"Dashboard: No authentication found, redirecting to login\");\n                    router.push(\"/login\");\n                    return;\n                }\n                const userData = JSON.parse(userStr);\n                console.log(\"Dashboard: User data:\", userData);\n                // Redirect based on user role\n                if (userData.role === \"Admin\") {\n                    router.push(\"/admin\");\n                    return;\n                } else if (userData.role === \"Supervisor\") {\n                    router.push(\"/supervisor\");\n                    return;\n                } else if (userData.role === \"Staff\") {\n                    router.push(\"/staff\");\n                    return;\n                }\n                setUser(userData);\n                const data = await (0,_lib_api_statistics__WEBPACK_IMPORTED_MODULE_3__.getHoursStatistics)();\n                console.log(\"Dashboard: Statistics received:\", data);\n                setStatistics(data);\n                // Fetch account statement\n                try {\n                    console.log(\"Dashboard: Fetching account statement...\");\n                    const statementData = await (0,_lib_api_statistics__WEBPACK_IMPORTED_MODULE_3__.getAccountStatement)();\n                    console.log(\"Dashboard: Account statement received:\", statementData);\n                    setStatement(statementData);\n                } catch (statementErr) {\n                    console.error(\"Dashboard: Error fetching account statement:\", statementErr);\n                // Don't fail the whole dashboard if statement fails\n                } finally{\n                    setStatementLoading(false);\n                }\n            } catch (err) {\n                console.error(\"Dashboard: Error fetching statistics:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Failed to fetch statistics\";\n                setError(errorMessage);\n                // If it's an authentication error, redirect to login\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"401\")) {\n                    console.log(\"Dashboard: Authentication error, redirecting to login\");\n                    router.push(\"/login\");\n                }\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchStatistics();\n    }, [\n        router\n    ]);\n    // Helper functions for formatting\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const formatHours = (hours)=>{\n        return hours.toFixed(2);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500\",\n                                children: \"Loading statistics...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-red-50 p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-red-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-red-800\",\n                                                children: \"Error Loading Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-red-700\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        if (false) {}\n                                                    },\n                                                    className: \"bg-red-100 px-2 py-1 text-sm font-medium text-red-800 rounded-md hover:bg-red-200\",\n                                                    children: \"Retry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-emerald-600\",\n                                            children: \"Staff Hall\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/dashboard\",\n                                                className: \"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/submit-job\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Submit Job\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/buy-hours\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Buy Hours\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500\",\n                                    onClick: ()=>{\n                                        sessionStorage.clear();\n                                        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n                                        window.location.href = \"/login\";\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold text-gray-900 mb-8\",\n                            children: \"Account Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 sm:grid-cols-3 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-emerald-50 p-6 rounded-xl border border-emerald-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-emerald-700\",\n                                                        children: \"Hours Bought\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-emerald-900\",\n                                                        children: statistics.hoursBought\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-emerald-50 p-6 rounded-xl border border-emerald-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-emerald-700\",\n                                                        children: \"Hours Used\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-emerald-900\",\n                                                        children: statistics.hoursUtilized\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-emerald-50 p-6 rounded-xl border border-emerald-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-emerald-700\",\n                                                        children: \"Available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-emerald-900\",\n                                                        children: statistics.hoursAvailable\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        statistics.hoursAvailable <= 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 bg-emerald-50 border border-emerald-200 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-8 w-8 text-emerald-500\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-emerald-800\",\n                                                        children: \"No Hours Available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-emerald-700\",\n                                                        children: \"You need to purchase hours to submit jobs and access our services.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/buy-hours\",\n                                            className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Buy Hours Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: \"32px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                hoursStatistics: statistics,\n                                onError: (error)=>{\n                                    console.error(\"JobList error:\", error);\n                                    if (error.includes(\"Authentication required\") || error.includes(\"401\")) {\n                                        router.push(\"/login\");\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 bg-white shadow rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                    children: \"Statement of Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-emerald-50 p-4 rounded-xl border border-emerald-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-5 w-5 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-emerald-700\",\n                                                                children: \"Total Hours Bought\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-emerald-900\",\n                                                                children: statement ? formatHours(statement.totalHoursBought) : formatHours(statistics.hoursBought)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-emerald-50 p-4 rounded-xl border border-emerald-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-5 w-5 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-emerald-700\",\n                                                                children: \"Total Hours Spent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-emerald-900\",\n                                                                children: statement ? formatHours(statement.totalHoursSpent) : formatHours(statistics.hoursUtilized)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-emerald-50 p-4 rounded-xl border border-emerald-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-5 w-5 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-emerald-700\",\n                                                                children: \"Hours Balance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-emerald-900\",\n                                                                children: statement ? formatHours(statement.currentHoursBalance) : formatHours(statistics.hoursAvailable)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-emerald-50 p-4 rounded-xl border border-emerald-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-5 w-5 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-emerald-700\",\n                                                                children: \"Amount Balance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-emerald-900\",\n                                                                children: statement ? formatCurrency(statement.currentAmountBalance) : \"$0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"min-w-full divide-y divide-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Reference\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Transaction\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Hours Bought\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Hours Spent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Adjustment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Balance Hours\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Balance Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"bg-white divide-y divide-gray-200\",\n                                                children: statementLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 10,\n                                                        className: \"px-6 py-8 text-center text-gray-500\",\n                                                        children: \"Loading transactions...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this) : statement && statement.entries.length > 0 ? statement.entries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: index % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: formatDate(entry.date)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${entry.type === \"Purchase\" ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                                                                    children: entry.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: entry.reference\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                                                                children: entry.transaction\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900\",\n                                                                children: entry.hoursBought > 0 ? formatHours(entry.hoursBought) : \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900\",\n                                                                children: entry.hoursSpent > 0 ? formatHours(entry.hoursSpent) : \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900\",\n                                                                children: entry.adjustment !== 0 ? formatHours(entry.adjustment) : \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900\",\n                                                                children: formatHours(entry.balanceHours)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900\",\n                                                                children: entry.amount !== 0 ? formatCurrency(entry.amount) : \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900\",\n                                                                children: formatCurrency(entry.balanceAmount)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 10,\n                                                        className: \"px-6 py-8 text-center text-gray-500\",\n                                                        children: \"No transactions found. Purchase hours or submit jobs to see your statement.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/JobList.tsx":
/*!************************************!*\
  !*** ./src/components/JobList.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_job__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/job */ \"(ssr)/./src/lib/job.ts\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(ssr)/./src/utils/statusDisplay.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Utility function to format enum names with proper spacing\nconst formatEnumName = (enumValue)=>{\n    return enumValue.replace(/([A-Z])/g, \" $1\").trim();\n};\nfunction JobList({ onError, hoursStatistics }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const fetchJobs = async ()=>{\n            try {\n                console.log(\"JobList: Starting to fetch jobs...\");\n                setIsLoading(true);\n                setError(null);\n                // Check if we're in the browser before making API calls\n                if (true) {\n                    console.log(\"JobList: Not in browser, stopping\");\n                    setIsLoading(false);\n                    return;\n                }\n                // Add delay to ensure authentication is ready\n                console.log(\"JobList: Waiting for authentication to be ready...\");\n                await new Promise((resolve)=>setTimeout(resolve, 200));\n                // Debug: Check authentication state\n                const token = sessionStorage.getItem(\"token\");\n                const user = sessionStorage.getItem(\"user\");\n                console.log(\"JobList: Auth check - Token exists:\", !!token, \"User exists:\", !!user);\n                console.log(\"JobList: SessionStorage length:\", sessionStorage.length);\n                if (token) {\n                    console.log(\"JobList: Token preview:\", token.substring(0, 20) + \"...\");\n                    console.log(\"JobList: Token length:\", token.length);\n                } else {\n                    console.log(\"JobList: No token found in sessionStorage\");\n                    console.log(\"JobList: All sessionStorage keys:\", Object.keys(sessionStorage));\n                    // Try to get token again after a short delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                    const retryToken = sessionStorage.getItem(\"token\");\n                    console.log(\"JobList: Retry token check:\", !!retryToken);\n                    if (!retryToken) {\n                        console.log(\"JobList: Still no token after retry, user might not be logged in\");\n                        setError(\"Please log in to view jobs\");\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                console.log(\"JobList: Calling getJobs()...\");\n                const jobsData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobs)();\n                console.log(\"JobList: Jobs fetched successfully:\", jobsData);\n                console.log(\"JobList: Number of jobs:\", jobsData.length);\n                setJobs(jobsData);\n            } catch (err) {\n                console.error(\"JobList: Error fetching jobs:\", err);\n                console.error(\"JobList: Error details:\", {\n                    message: err instanceof Error ? err.message : \"Unknown error\",\n                    stack: err instanceof Error ? err.stack : undefined\n                });\n                const errorMessage = err instanceof Error ? err.message : \"Failed to fetch jobs\";\n                setError(errorMessage);\n                onError?.(errorMessage);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchJobs();\n    }, [\n        mounted,\n        onError\n    ]);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        fontSize: \"18px\",\n                        fontWeight: \"500\",\n                        margin: \"0 0 16px 0\"\n                    },\n                    children: \"Work History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#6b7280\",\n                        margin: \"0\"\n                    },\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    const getStatusBadge = (status)=>{\n        const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\n        const userRole = user.role || \"User\";\n        const displayText = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getRoleBasedStatusDisplay)(status, userRole);\n        const colors = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getStatusColors)(status);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            style: {\n                display: \"inline-flex\",\n                alignItems: \"center\",\n                padding: \"4px 12px\",\n                borderRadius: \"9999px\",\n                fontSize: \"12px\",\n                fontWeight: \"500\",\n                backgroundColor: colors.bg,\n                color: colors.text,\n                border: `1px solid ${colors.border}`\n            },\n            children: displayText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    height: \"128px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: \"#6b7280\"\n                    },\n                    children: \"Loading jobs...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    borderRadius: \"6px\",\n                    backgroundColor: \"#fef2f2\",\n                    padding: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flexShrink: 0\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                style: {\n                                    height: \"20px\",\n                                    width: \"20px\",\n                                    color: \"#f87171\"\n                                },\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginLeft: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        color: \"#991b1b\",\n                                        margin: \"0 0 8px 0\"\n                                    },\n                                    children: \"Error Loading Jobs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        color: \"#7f1d1d\",\n                                        margin: \"0\"\n                                    },\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    if (jobs.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"500\",\n                            color: \"#111827\",\n                            margin: \"0 0 24px 0\"\n                        },\n                        children: \"Work History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"16px\",\n                            marginBottom: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"Total Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"New\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"32px 0\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                style: {\n                                    margin: \"0 auto\",\n                                    height: \"48px\",\n                                    width: \"48px\",\n                                    color: \"#9ca3af\"\n                                },\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    color: \"#111827\",\n                                    margin: \"8px 0\"\n                                },\n                                children: \"No jobs found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginTop: \"4px\",\n                                    fontSize: \"14px\",\n                                    color: \"#6b7280\",\n                                    margin: \"4px 0 24px 0\"\n                                },\n                                children: hoursStatistics && hoursStatistics.hoursAvailable <= 0 ? \"Purchase hours to start submitting jobs.\" : \"Get started by submitting your first job.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            hoursStatistics && hoursStatistics.hoursAvailable > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/submit-job\",\n                                    style: {\n                                        display: \"inline-flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 16px\",\n                                        border: \"none\",\n                                        boxShadow: \"0 1px 2px 0 rgba(0, 0, 0, 0.05)\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        borderRadius: \"6px\",\n                                        color: \"white\",\n                                        backgroundColor: \"#059669\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Submit Job\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    // Calculate summary statistics\n    const totalJobs = jobs.length;\n    const completedJobs = jobs.filter((job)=>job.status === \"Closed\").length;\n    const pendingJobs = jobs.filter((job)=>job.status === \"New\").length;\n    const inProgressJobs = jobs.filter((job)=>job.status === \"InProgress\").length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: \"white\",\n            boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n            borderRadius: \"8px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: \"24px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        fontSize: \"18px\",\n                        fontWeight: \"500\",\n                        color: \"#111827\",\n                        margin: \"0 0 24px 0\"\n                    },\n                    children: \"Work History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"grid\",\n                        gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                        gap: \"16px\",\n                        marginBottom: \"24px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"Total Jobs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: totalJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M5 13l4 4L19 7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: completedJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"In Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: inProgressJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"New\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: pendingJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        overflow: \"hidden\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        style: {\n                            minWidth: \"100%\",\n                            borderCollapse: \"collapse\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                style: {\n                                    backgroundColor: \"#f9fafb\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Job\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Created\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                style: {\n                                    backgroundColor: \"white\"\n                                },\n                                children: jobs.map((job, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            borderTop: index > 0 ? \"1px solid #e5e7eb\" : \"none\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"14px\",\n                                                                fontWeight: \"500\",\n                                                                color: \"#059669\",\n                                                                cursor: \"pointer\",\n                                                                textDecoration: \"underline\"\n                                                            },\n                                                            onClick: ()=>router.push(`/jobs/${job.id}`),\n                                                            onMouseEnter: (e)=>{\n                                                                e.currentTarget.style.color = \"#047857\";\n                                                            },\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.style.color = \"#059669\";\n                                                            },\n                                                            children: job.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"14px\",\n                                                                color: \"#6b7280\",\n                                                                overflow: \"hidden\",\n                                                                textOverflow: \"ellipsis\",\n                                                                maxWidth: \"300px\"\n                                                            },\n                                                            children: job.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: \"#111827\"\n                                                        },\n                                                        children: formatEnumName(job.jobType)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: \"#6b7280\"\n                                                        },\n                                                        children: formatEnumName(job.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: getStatusBadge(job.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\",\n                                                    fontSize: \"14px\",\n                                                    color: \"#6b7280\"\n                                                },\n                                                children: formatDate(job.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, job.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 404,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n        lineNumber: 399,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/JobList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/statistics.ts":
/*!***********************************!*\
  !*** ./src/lib/api/statistics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccountStatement: () => (/* binding */ getAccountStatement),\n/* harmony export */   getHoursStatistics: () => (/* binding */ getHoursStatistics)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:5000\" || 0;\nconst getHoursStatistics = async ()=>{\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    const userStr = sessionStorage.getItem(\"user\");\n    if (!token || !userStr) {\n        throw new Error(\"Authentication required\");\n    }\n    const user = JSON.parse(userStr);\n    console.log(\"Fetching statistics for clientId:\", user.clientId);\n    try {\n        const headers = {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        };\n        console.log(\"Request headers:\", headers);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/statistics/hours/${user.clientId}`, {\n            headers\n        });\n        console.log(\"API Response:\", response.data);\n        return response.data;\n    } catch (error) {\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"API Error:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data,\n                clientId: user.clientId,\n                url: `${API_URL}/api/statistics/hours/${user.clientId}`\n            });\n            const status = error.response?.status;\n            const errorData = error.response?.data;\n            if (status === 401) {\n                throw new Error(\"Authentication required - please log in again\");\n            } else if (status === 404) {\n                throw new Error(\"Client data not found - please contact support\");\n            } else if (status === 500) {\n                throw new Error(\"Server error - please try again later\");\n            } else {\n                const message = typeof errorData === \"string\" ? errorData : errorData?.message || error.message || \"Unknown error occurred\";\n                throw new Error(`Failed to fetch statistics: ${message}`);\n            }\n        } else if (error instanceof Error) {\n            throw new Error(`Failed to fetch statistics: ${error.message}`);\n        } else {\n            throw new Error(\"Failed to fetch statistics: An unknown error occurred\");\n        }\n    }\n};\nconst getAccountStatement = async ()=>{\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    const userStr = sessionStorage.getItem(\"user\");\n    if (!token || !userStr) {\n        throw new Error(\"Authentication required\");\n    }\n    const user = JSON.parse(userStr);\n    console.log(\"Fetching account statement for clientId:\", user.clientId);\n    try {\n        const headers = {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        };\n        console.log(\"Request headers:\", headers);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/statistics/statement/${user.clientId}`, {\n            headers\n        });\n        console.log(\"Account Statement API Response:\", response.data);\n        return response.data;\n    } catch (error) {\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"Account Statement API Error:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data,\n                clientId: user.clientId,\n                url: `${API_URL}/api/statistics/statement/${user.clientId}`\n            });\n            const status = error.response?.status;\n            const errorData = error.response?.data;\n            if (status === 401) {\n                throw new Error(\"Authentication required - please log in again\");\n            } else if (status === 404) {\n                throw new Error(\"Account statement not found - please contact support\");\n            } else if (status === 500) {\n                throw new Error(\"Server error - please try again later\");\n            } else {\n                const message = typeof errorData === \"string\" ? errorData : errorData?.message || error.message || \"Unknown error occurred\";\n                throw new Error(`Failed to fetch account statement: ${message}`);\n            }\n        } else if (error instanceof Error) {\n            throw new Error(`Failed to fetch account statement: ${error.message}`);\n        } else {\n            throw new Error(\"Failed to fetch account statement: An unknown error occurred\");\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/statistics.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/job.ts":
/*!************************!*\
  !*** ./src/lib/job.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   completeJob: () => (/* binding */ completeJob),\n/* harmony export */   createJob: () => (/* binding */ createJob),\n/* harmony export */   getJobById: () => (/* binding */ getJobById),\n/* harmony export */   getJobs: () => (/* binding */ getJobs),\n/* harmony export */   markJobSatisfied: () => (/* binding */ markJobSatisfied),\n/* harmony export */   reviewCompletedJob: () => (/* binding */ reviewCompletedJob)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:5000\" || 0;\nconst getJobs = async ()=>{\n    console.log(\"getJobs: Starting...\");\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        console.log(\"getJobs: Not in browser, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    // Add a small delay to ensure sessionStorage is ready\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    const token = sessionStorage.getItem(\"token\");\n    const user = sessionStorage.getItem(\"user\");\n    console.log(\"getJobs: Token exists:\", !!token);\n    console.log(\"getJobs: User exists:\", !!user);\n    console.log(\"getJobs: SessionStorage keys:\", Object.keys(sessionStorage));\n    if (token) {\n        console.log(\"getJobs: Token preview:\", token.substring(0, 20) + \"...\");\n        console.log(\"getJobs: Token length:\", token.length);\n    } else {\n        console.log(\"getJobs: Token is null/undefined/empty\");\n        console.log(\"getJobs: All sessionStorage items:\", {\n            token: sessionStorage.getItem(\"token\"),\n            user: sessionStorage.getItem(\"user\")\n        });\n    }\n    if (!token) {\n        console.log(\"getJobs: No token found, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    console.log(\"getJobs: Making API request to:\", `${API_URL}/api/jobs`);\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/jobs`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        console.log(\"getJobs: Response status:\", response.status);\n        console.log(\"getJobs: Response data type:\", typeof response.data);\n        console.log(\"getJobs: Response data length:\", Array.isArray(response.data) ? response.data.length : \"Not an array\");\n        console.log(\"getJobs: Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"getJobs: API request failed:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"getJobs: Axios error details:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data\n            });\n            if (error.response?.status === 401) {\n                throw new Error(\"Authentication required\");\n            }\n        }\n        throw error;\n    }\n};\nconst getJobById = async (id)=>{\n    console.log(\"getJobById: Starting for job ID:\", id);\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        console.log(\"getJobById: Not in browser, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        console.log(\"getJobById: No token found\");\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        console.log(\"getJobById: Making API request to:\", `${API_URL}/api/jobs/${id}`);\n        console.log(\"getJobById: Using token:\", token.substring(0, 20) + \"...\");\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/jobs/${id}`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        console.log(\"getJobById: Response status:\", response.status);\n        console.log(\"getJobById: Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"getJobById: API request failed:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"getJobById: Axios error details:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data\n            });\n            if (error.response?.status === 401) {\n                throw new Error(\"Authentication required\");\n            }\n            if (error.response?.status === 404) {\n                throw new Error(\"Job not found\");\n            }\n            if (error.response?.status === 403) {\n                throw new Error(\"Access denied\");\n            }\n        }\n        throw error;\n    }\n};\nconst createJob = async (jobData)=>{\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/api/jobs`, jobData, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    return response.data;\n};\n// New workflow API functions\nconst completeJob = async (jobId, outputDetails)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/complete`, {\n        outputDetails\n    }, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\nconst reviewCompletedJob = async (jobId)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/review-completed`, {}, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\nconst markJobSatisfied = async (jobId)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/mark-satisfied`, {}, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/job.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: () => (/* binding */ getRoleBasedStatusDisplay),\n/* harmony export */   getStatusColors: () => (/* binding */ getStatusColors),\n/* harmony export */   getStatusTailwindClasses: () => (/* binding */ getStatusTailwindClasses)\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Debug logging to see what status values we're receiving\n    console.log(\"Status Display Debug:\", {\n        originalStatus: status,\n        userRole,\n        normalizedStatus: status.replace(/[-\\s]/g, \"\")\n    });\n    // Normalize status to handle both backend enum names and frontend display names\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    switch(userRole){\n        case \"User\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"Submitted\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                case \"AwaitingReview\":\n                    return \"Awaiting Supervisor Review\";\n                case \"Released\":\n                    return \"Completed\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Supervisor\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting My Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Staff\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned to Me\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting Supervisor Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Admin\":\n            // Admin sees the system status names\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        default:\n            // Default to system status names\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n    }\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/statusDisplay.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"927152f9cf66\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhZmZpbmdhcHAtd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83NzkzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTI3MTUyZjljZjY2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staff Hall\",\n    description: \"A modern staffing solution\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTUE7QUFIZ0I7QUFLZixNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULCtKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1N0YWZmIEhhbGwnLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuIHN0YWZmaW5nIHNvbHV0aW9uJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2Zhdmljb24uaWNvP2VhNzAiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();