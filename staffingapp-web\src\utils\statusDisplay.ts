/**
 * Maps job statuses to role-specific display names
 */
export const getRoleBasedStatusDisplay = (status: string, userRole: string): string => {
  // Debug logging with more detail
  console.log('🔍 getRoleBasedStatusDisplay called:', {
    originalStatus: status,
    userRole,
    timestamp: new Date().toISOString()
  });

  // Normalize status to handle both backend enum names and frontend display names
  const normalizedStatus = status.replace(/[-\s]/g, '');
  console.log('📝 Normalized status:', normalizedStatus);

  let result = status; // Default fallback

  switch (userRole) {
    case 'User': // Client role
      switch (normalizedStatus) {
        case 'New':
          result = 'Submitted';
          break;
        case 'Returned':
          result = 'Returned';
          break;
        case 'Assigned':
          result = 'Assigned';
          break;
        case 'InProgress':
          result = 'In Progress';
          break;
        case 'AwaitingReview':
          result = 'Awaiting Supervisor Review';
          break;
        case 'Released':
          result = 'Completed';
          break;
        case 'Closed':
          result = 'Closed';
          break;
        default:
          result = status;
      }
      break;

    case 'Supervisor':
      switch (normalizedStatus) {
        case 'New':
          result = 'New';
          break;
        case 'Returned':
          result = 'Returned';
          break;
        case 'Assigned':
          result = 'Assigned';
          break;
        case 'InProgress':
          result = 'In Progress';
          break;
        case 'AwaitingReview':
          result = 'Awaiting My Review';
          break;
        case 'Released':
          result = 'Released';
          break;
        case 'Closed':
          result = 'Closed';
          break;
        default:
          result = status;
      }
      break;

    case 'Staff':
      switch (normalizedStatus) {
        case 'New':
          result = 'New';
          break;
        case 'Returned':
          result = 'Returned';
          break;
        case 'Assigned':
          result = 'Assigned to Me';
          break;
        case 'InProgress':
          result = 'In Progress';
          break;
        case 'AwaitingReview':
          result = 'Awaiting Supervisor Review';
          break;
        case 'Released':
          result = 'Released';
          break;
        case 'Closed':
          result = 'Closed';
          break;
        default:
          result = status;
      }
      break;

    case 'Admin':
      // Admin sees the system status names
      switch (normalizedStatus) {
        case 'New':
          result = 'New';
          break;
        case 'Returned':
          result = 'Returned';
          break;
        case 'Assigned':
          result = 'Assigned';
          break;
        case 'InProgress':
          result = 'In Progress';
          break;
        case 'AwaitingReview':
          result = 'Awaiting Review';
          break;
        case 'Released':
          result = 'Released';
          break;
        case 'Closed':
          result = 'Closed';
          break;
        default:
          result = status;
      }
      break;

    default:
      // Default to system status names
      switch (normalizedStatus) {
        case 'New':
          result = 'New';
          break;
        case 'Returned':
          result = 'Returned';
          break;
        case 'Assigned':
          result = 'Assigned';
          break;
        case 'InProgress':
          result = 'In Progress';
          break;
        case 'AwaitingReview':
          result = 'Awaiting Review';
          break;
        case 'Released':
          result = 'Released';
          break;
        case 'Closed':
          result = 'Closed';
          break;
        default:
          result = status;
      }
      break;
  }

  console.log('✅ Status display result:', { originalStatus: status, userRole, result });
  return result;
};

/**
 * Gets the color scheme for a job status badge
 */
export const getStatusColors = (status: string): { bg: string; text: string; border: string } => {
  const normalizedStatus = status.replace(/[-\s]/g, '');

  const statusColors: { [key: string]: { bg: string; text: string; border: string } } = {
    'New': { bg: '#ecfdf5', text: '#065f46', border: '#d1fae5' },
    'Returned': { bg: '#fef3c7', text: '#92400e', border: '#fde68a' },
    'Assigned': { bg: '#eff6ff', text: '#1e40af', border: '#bfdbfe' },
    'InProgress': { bg: '#f0fdf4', text: '#14532d', border: '#bbf7d0' },
    'AwaitingReview': { bg: '#fef3c7', text: '#92400e', border: '#fde68a' },
    'Released': { bg: '#e0e7ff', text: '#3730a3', border: '#c7d2fe' },
    'Closed': { bg: '#dcfce7', text: '#166534', border: '#bbf7d0' }
  };

  return statusColors[normalizedStatus] || { bg: '#f3f4f6', text: '#4b5563', border: '#d1d5db' };
};

/**
 * Gets Tailwind CSS classes for status badges
 */
export const getStatusTailwindClasses = (status: string): string => {
  const normalizedStatus = status.replace(/[-\s]/g, '');

  const statusClasses: { [key: string]: string } = {
    'New': 'bg-emerald-100 text-emerald-800',
    'Returned': 'bg-yellow-100 text-yellow-800',
    'Assigned': 'bg-blue-100 text-blue-800',
    'InProgress': 'bg-emerald-100 text-emerald-800',
    'AwaitingReview': 'bg-yellow-100 text-yellow-800',
    'Released': 'bg-purple-100 text-purple-800',
    'Closed': 'bg-green-100 text-green-800'
  };

  return statusClasses[normalizedStatus] || 'bg-gray-100 text-gray-800';
};
