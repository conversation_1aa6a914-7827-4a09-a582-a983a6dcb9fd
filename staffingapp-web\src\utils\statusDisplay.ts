import { JobStatus, UserRole } from '@/types/models';

/**
 * Maps job statuses to role-specific display names
 */
export const getRoleBasedStatusDisplay = (status: string, userRole: string): string => {
  // Handle the enum values that might come with hyphens or spaces
  const normalizedStatus = status.replace(/[-\s]/g, '');

  switch (userRole) {
    case 'User': // Client role
      switch (normalizedStatus) {
        case 'New':
          return 'Submitted';
        case 'Returned':
          return 'Returned';
        case 'Assigned':
          return 'Assigned';
        case 'InProgress':
        case 'In-Progress':
          return 'In Progress';
        case 'AwaitingReview':
        case 'Awaiting Review':
          return 'Awaiting Supervisor Review';
        case 'Released':
          return 'Completed';
        case 'Closed':
          return 'Closed';
        default:
          return status;
      }
      
    case 'Staff':
      switch (normalizedStatus) {
        case 'New':
          return 'New';
        case 'Returned':
          return 'Returned';
        case 'Assigned':
          return 'Assigned to Me';
        case 'InProgress':
        case 'In-Progress':
          return 'In Progress';
        case 'AwaitingReview':
        case 'Awaiting Review':
          return 'Awaiting Review';
        case 'Released':
          return 'Released';
        case 'Closed':
          return 'Closed';
        default:
          return status;
      }
      
    case 'Supervisor':
      switch (normalizedStatus) {
        case 'New':
          return 'New';
        case 'Returned':
          return 'Returned';
        case 'Assigned':
          return 'Assigned';
        case 'InProgress':
        case 'In-Progress':
          return 'In Progress';
        case 'AwaitingReview':
        case 'Awaiting Review':
          return 'Awaiting Review';
        case 'Released':
          return 'Released';
        case 'Closed':
          return 'Closed';
        default:
          return status;
      }
      
    case 'Admin':
      // Admin sees the raw status names
      switch (normalizedStatus) {
        case 'New':
          return 'New';
        case 'Returned':
          return 'Returned';
        case 'Assigned':
          return 'Assigned';
        case 'InProgress':
        case 'In-Progress':
          return 'In Progress';
        case 'AwaitingReview':
        case 'Awaiting Review':
          return 'Awaiting Review';
        case 'Released':
          return 'Released';
        case 'Closed':
          return 'Closed';
        default:
          return status;
      }
      
    default:
      return status;
  }
};

/**
 * Gets the color scheme for a job status badge
 */
export const getStatusColors = (status: string): { bg: string; text: string; border: string } => {
  const normalizedStatus = status.replace(/[-\s]/g, '');
  
  const statusColors: { [key: string]: { bg: string; text: string; border: string } } = {
    'New': { bg: '#ecfdf5', text: '#065f46', border: '#d1fae5' },
    'Returned': { bg: '#fef3c7', text: '#92400e', border: '#fde68a' },
    'Assigned': { bg: '#eff6ff', text: '#1e40af', border: '#bfdbfe' },
    'InProgress': { bg: '#f0fdf4', text: '#14532d', border: '#bbf7d0' },
    'In-Progress': { bg: '#f0fdf4', text: '#14532d', border: '#bbf7d0' },
    'AwaitingReview': { bg: '#fef3c7', text: '#92400e', border: '#fde68a' },
    'Awaiting Review': { bg: '#fef3c7', text: '#92400e', border: '#fde68a' },
    'Released': { bg: '#e0e7ff', text: '#3730a3', border: '#c7d2fe' },
    'Closed': { bg: '#dcfce7', text: '#166534', border: '#bbf7d0' }
  };

  return statusColors[normalizedStatus] || { bg: '#f3f4f6', text: '#4b5563', border: '#d1d5db' };
};

/**
 * Gets Tailwind CSS classes for status badges
 */
export const getStatusTailwindClasses = (status: string): string => {
  const normalizedStatus = status.replace(/[-\s]/g, '');
  
  const statusClasses: { [key: string]: string } = {
    'New': 'bg-emerald-100 text-emerald-800',
    'Returned': 'bg-yellow-100 text-yellow-800',
    'Assigned': 'bg-blue-100 text-blue-800',
    'InProgress': 'bg-emerald-100 text-emerald-800',
    'In-Progress': 'bg-emerald-100 text-emerald-800',
    'AwaitingReview': 'bg-yellow-100 text-yellow-800',
    'Awaiting Review': 'bg-yellow-100 text-yellow-800',
    'Released': 'bg-purple-100 text-purple-800',
    'Closed': 'bg-green-100 text-green-800'
  };

  return statusClasses[normalizedStatus] || 'bg-gray-100 text-gray-800';
};
