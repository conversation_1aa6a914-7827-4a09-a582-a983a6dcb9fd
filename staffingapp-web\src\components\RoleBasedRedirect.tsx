'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function RoleBasedRedirect() {
  const router = useRouter();

  useEffect(() => {
    const checkUserRole = () => {
      const userStr = sessionStorage.getItem('user');
      
      if (!userStr) {
        router.push('/login');
        return;
      }

      const user = JSON.parse(userStr);
      
      // Redirect based on user role
      switch (user.role) {
        case 'Admin':
          router.push('/admin');
          break;
        case 'Supervisor':
          router.push('/supervisor');
          break;
        case 'Staff':
          router.push('/staff');
          break;
        case 'User':
        default:
          router.push('/dashboard');
          break;
      }
    };

    checkUserRole();
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Redirecting to your dashboard...</p>
      </div>
    </div>
  );
}
