using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using StaffingApp.Api.Data;
using StaffingApp.Api.Models;
using StaffingApp.Api.Models.DTOs;

namespace StaffingApp.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HoursPurchaseController : ControllerBase
    {
        private readonly StaffingContext _context;

        public HoursPurchaseController(StaffingContext context)
        {
            _context = context;
        }

        [HttpGet("rates")]
        public async Task<ActionResult<PaymentRateDTO>> GetCurrentRate()
        {
            var rate = await _context.PaymentRates
                .Where(r => r.IsActive && r.EffectiveFrom <= DateTime.UtcNow && 
                    (!r.EffectiveTo.HasValue || r.EffectiveTo > DateTime.UtcNow))
                .OrderByDescending(r => r.EffectiveFrom)
                .FirstOrDefaultAsync();

            if (rate == null)
                return NotFound("No active payment rate found");

            return Ok(new PaymentRateDTO
            {
                Id = rate.Id,
                RatePerHour = rate.RatePerHour,
                Currency = rate.Currency,
                EffectiveFrom = rate.EffectiveFrom,
                EffectiveTo = rate.EffectiveTo,
                IsActive = rate.IsActive
            });
        }

        [HttpPost("purchase")]
        public async Task<ActionResult<HoursPurchase>> CreatePurchase([FromBody] CreateHoursPurchaseDTO dto)
        {
            // Get the current user ID from the JWT token
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized("Invalid user token");
            }

            var currentRate = await _context.PaymentRates
                .Where(r => r.IsActive && r.EffectiveFrom <= DateTime.UtcNow &&
                    (!r.EffectiveTo.HasValue || r.EffectiveTo > DateTime.UtcNow))
                .OrderByDescending(r => r.EffectiveFrom)
                .FirstOrDefaultAsync();

            if (currentRate == null)
                return BadRequest("No active payment rate found");

            // Get the user entity
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return BadRequest("User not found");

            var purchase = new HoursPurchase
            {
                UserId = userId,
                User = user,
                Hours = dto.Hours,
                Amount = dto.Hours * currentRate.RatePerHour,
                Currency = currentRate.Currency,
                PaymentReference = dto.PaymentReference,
                PaymentProof = dto.PaymentProof,
                Status = HoursPurchaseStatus.Pending,
                CreatedAt = DateTime.UtcNow,
                PaymentRateId = currentRate.Id,
                PaymentRate = currentRate
            };

            _context.HoursPurchases.Add(purchase);
            await _context.SaveChangesAsync();

            // Auto-approve in development for testing
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
            {
                purchase.Status = HoursPurchaseStatus.Approved;
                purchase.ProcessedAt = DateTime.UtcNow;
                purchase.ProcessedBy = userId;

                // Add purchased hours to client's balance
                var client = await _context.Clients.FirstOrDefaultAsync(c => c.Id == user.ClientId);
                if (client != null)
                {
                    client.HoursBought += purchase.Hours;
                }

                await _context.SaveChangesAsync();
            }

            return CreatedAtAction(nameof(GetPurchase), new { id = purchase.Id }, purchase);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HoursPurchase>> GetPurchase(int id)
        {
            // Get the current user ID from the JWT token
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized("Invalid user token");
            }

            var purchase = await _context.HoursPurchases
                .Include(h => h.User)
                .Include(h => h.PaymentRate)
                .FirstOrDefaultAsync(h => h.Id == id);

            if (purchase == null)
                return NotFound();

            if (purchase.UserId != userId && !User.IsInRole("Admin"))
                return Forbid();

            return purchase;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<HoursPurchase>>> GetPurchases()
        {
            var query = _context.HoursPurchases.AsQueryable();

            if (!User.IsInRole("Admin"))
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }
                query = query.Where(h => h.UserId == userId);
            }

            return await query
                .Include(h => h.User)
                .Include(h => h.PaymentRate)
                .OrderByDescending(h => h.CreatedAt)
                .ToListAsync();
        }

        [HttpPut("{id}/approve")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> ApprovePurchase(int id)
        {
            // Get the current user ID from the JWT token
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized("Invalid user token");
            }

            var purchase = await _context.HoursPurchases
                .Include(p => p.User)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (purchase == null)
                return NotFound();

            if (purchase.Status != HoursPurchaseStatus.Pending)
                return BadRequest("Can only approve pending purchases");

            // Update purchase status
            purchase.Status = HoursPurchaseStatus.Approved;
            purchase.ProcessedAt = DateTime.UtcNow;
            purchase.ProcessedBy = userId;

            // Add purchased hours to client's balance
            var client = await _context.Clients.FirstOrDefaultAsync(c => c.Id == purchase.User.ClientId);
            if (client != null)
            {
                client.HoursBought += purchase.Hours;
            }

            await _context.SaveChangesAsync();

            return Ok();
        }

        [HttpPut("{id}/reject")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> RejectPurchase(int id, [FromBody] string notes)
        {
            // Get the current user ID from the JWT token
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized("Invalid user token");
            }

            var purchase = await _context.HoursPurchases.FindAsync(id);

            if (purchase == null)
                return NotFound();

            if (purchase.Status != HoursPurchaseStatus.Pending)
                return BadRequest("Can only reject pending purchases");

            purchase.Status = HoursPurchaseStatus.Rejected;
            purchase.ProcessedAt = DateTime.UtcNow;
            purchase.ProcessedBy = userId;
            purchase.Notes = notes;

            await _context.SaveChangesAsync();

            return Ok();
        }
    }
}