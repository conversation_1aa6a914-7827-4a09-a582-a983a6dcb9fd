'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Job, User, JobStatus, UserRole } from '@/types/models';
import { getRoleBasedStatusDisplay, getStatusTailwindClasses } from '@/utils/statusDisplay';

// Utility function to format enum names with proper spacing
const formatEnumName = (enumValue: string): string => {
  return enumValue.replace(/([A-Z])/g, ' $1').trim();
};

export default function SupervisorDashboard() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [staffMembers, setStaffMembers] = useState<User[]>([]);
  const [activeTab, setActiveTab] = useState('pending');
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [assignmentData, setAssignmentData] = useState({
    staffId: '',
    notes: ''
  });

  useEffect(() => {
    const checkAuth = () => {
      const token = sessionStorage.getItem('token');
      const userStr = sessionStorage.getItem('user');
      
      if (!token || !userStr) {
        router.push('/login');
        return;
      }

      const userData = JSON.parse(userStr);
      if (userData.role !== 'Supervisor') {
        router.push('/dashboard');
        return;
      }

      setUser(userData);
      fetchJobs();
      fetchStaffMembers();
    };

    checkAuth();
  }, [router]);

  const fetchJobs = async () => {
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/jobs/all', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const jobsData = await response.json();
        setJobs(jobsData);
      } else {
        console.error('Failed to fetch jobs:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStaffMembers = async () => {
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/auth/users/staff', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const staffData = await response.json();
        setStaffMembers(staffData);
      } else {
        console.error('Failed to fetch staff members:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching staff members:', error);
    }
  };

  const handleLogout = () => {
    sessionStorage.clear();
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    window.location.href = '/login';
  };

  const handleAssignJob = (job: Job) => {
    setSelectedJob(job);
    setShowAssignModal(true);
    setAssignmentData({ staffId: '', notes: '' });
  };

  const handleRejectJob = async (jobId: number) => {
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch(`http://localhost:5000/api/jobs/${jobId}/reject`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        fetchJobs(); // Refresh the jobs list
      }
    } catch (error) {
      console.error('Error rejecting job:', error);
    }
  };

  const submitAssignment = async () => {
    if (!selectedJob || !assignmentData.staffId) return;

    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch(`http://localhost:5000/api/jobs/${selectedJob.id}/assign`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          staffId: parseInt(assignmentData.staffId),
          notes: assignmentData.notes
        })
      });

      if (response.ok) {
        setShowAssignModal(false);
        setSelectedJob(null);
        setAssignmentData({ staffId: '', notes: '' });
        fetchJobs(); // Refresh the jobs list
      }
    } catch (error) {
      console.error('Error assigning job:', error);
    }
  };

  const getJobsByStatus = (status: string) => {
    return jobs.filter(job => job.status === status);
  };

  const getStatusColor = (status: string) => {
    return getStatusTailwindClasses(status);
  };

  const getStatusDisplay = (status: string) => {
    return getRoleBasedStatusDisplay(status, 'Supervisor');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading supervisor dashboard...</p>
        </div>
      </div>
    );
  }

  const pendingJobs = getJobsByStatus('New');
  const underReviewJobs = getJobsByStatus('Returned');
  const assignedJobs = getJobsByStatus('Assigned');
  const inProgressJobs = getJobsByStatus('In-Progress');
  const completedJobs = getJobsByStatus('Awaiting Review'); // Jobs completed by staff, awaiting supervisor review

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">Staff Hall - Supervisor Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                Welcome, {user?.firstName} {user?.lastName}
              </span>
              <button
                onClick={handleLogout}
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="pt-24 py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{pendingJobs.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Pending Review</dt>
                      <dd className="text-lg font-medium text-gray-900">{pendingJobs.length} jobs</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{assignedJobs.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Assigned</dt>
                      <dd className="text-lg font-medium text-gray-900">{assignedJobs.length} jobs</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{inProgressJobs.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                      <dd className="text-lg font-medium text-gray-900">{inProgressJobs.length} jobs</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{completedJobs.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Awaiting Review</dt>
                      <dd className="text-lg font-medium text-gray-900">{completedJobs.length} jobs</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{staffMembers.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Staff Members</dt>
                      <dd className="text-lg font-medium text-gray-900">{staffMembers.length} active</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="bg-white shadow rounded-lg">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                {[
                  { id: 'pending', name: 'Pending Review', count: pendingJobs.length },
                  { id: 'assigned', name: 'Assigned Jobs', count: assignedJobs.length },
                  { id: 'progress', name: 'In Progress', count: inProgressJobs.length },
                  { id: 'completed', name: 'Awaiting Review', count: completedJobs.length },
                  { id: 'staff', name: 'Staff Management', count: staffMembers.length },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-emerald-500 text-emerald-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                  >
                    {tab.name}
                    <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                      {tab.count}
                    </span>
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'pending' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Jobs Pending Review</h3>
                  {pendingJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No pending jobs to review</p>
                  ) : (
                    <div className="space-y-4">
                      {pendingJobs.map((job) => (
                        <div key={job.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="text-lg font-medium text-gray-900">{job.title}</h4>
                              <p className="text-gray-600 mt-1">{job.description}</p>
                              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                <span>Type: {formatEnumName(job.jobType)}</span>
                                <span>Category: {formatEnumName(job.category)}</span>
                                <span>Submitted: {formatDate(job.createdAt)}</span>
                              </div>
                            </div>
                            <div className="ml-4 flex space-x-2">
                              <button
                                onClick={() => router.push(`/jobs/${job.id}`)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                              >
                                View Details
                              </button>
                              <button
                                onClick={() => handleAssignJob(job)}
                                className="bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1 rounded text-sm"
                              >
                                Assign
                              </button>
                              <button
                                onClick={() => handleRejectJob(job.id)}
                                className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                              >
                                Reject
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'assigned' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Assigned Jobs</h3>
                  {assignedJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No assigned jobs</p>
                  ) : (
                    <div className="space-y-4">
                      {assignedJobs.map((job) => (
                        <div key={job.id} className="border border-gray-200 rounded-lg p-4 bg-purple-50">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="text-lg font-medium text-gray-900">{job.title}</h4>
                              <p className="text-gray-600 mt-1">{job.description}</p>
                              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                <span>Type: {formatEnumName(job.jobType)}</span>
                                <span>Category: {formatEnumName(job.category)}</span>
                                <span>Assigned: {job.assignedAt ? formatDate(job.assignedAt) : 'N/A'}</span>
                              </div>
                              {job.assignedTo && (
                                <div className="mt-2">
                                  <span className="text-sm text-gray-600">
                                    Assigned to: <strong>{job.assignedTo.firstName} {job.assignedTo.lastName}</strong>
                                  </span>
                                </div>
                              )}
                              {job.supervisorNotes && (
                                <div className="mt-2 p-2 bg-blue-50 rounded">
                                  <p className="text-sm text-blue-800">
                                    <strong>Notes:</strong> {job.supervisorNotes}
                                  </p>
                                </div>
                              )}
                            </div>
                            <div className="ml-4 flex items-center space-x-2">
                              <button
                                onClick={() => router.push(`/jobs/${job.id}`)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                              >
                                View Details
                              </button>
                              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(job.status)}`}>
                                {getStatusDisplay(job.status)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'progress' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Jobs In Progress</h3>
                  {inProgressJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No jobs in progress</p>
                  ) : (
                    <div className="space-y-4">
                      {inProgressJobs.map((job) => (
                        <div key={job.id} className="border border-gray-200 rounded-lg p-4 bg-emerald-50">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="text-lg font-medium text-gray-900">{job.title}</h4>
                              <p className="text-gray-600 mt-1">{job.description}</p>
                              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                <span>Type: {formatEnumName(job.jobType)}</span>
                                <span>Category: {formatEnumName(job.category)}</span>
                                <span>Started: {job.assignedAt ? formatDate(job.assignedAt) : 'N/A'}</span>
                              </div>
                              {job.assignedTo && (
                                <div className="mt-2">
                                  <span className="text-sm text-gray-600">
                                    Being worked on by: <strong>{job.assignedTo.firstName} {job.assignedTo.lastName}</strong>
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="ml-4 flex items-center space-x-2">
                              <button
                                onClick={() => router.push(`/jobs/${job.id}`)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                              >
                                View Details
                              </button>
                              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(job.status)}`}>
                                {getStatusDisplay(job.status)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'completed' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Jobs Awaiting Supervisor Review</h3>
                  {completedJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No completed jobs awaiting review</p>
                  ) : (
                    <div className="space-y-4">
                      {completedJobs.map((job) => (
                        <div key={job.id} className="border border-gray-200 rounded-lg p-4 bg-green-50">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="text-lg font-medium text-gray-900">{job.title}</h4>
                              <p className="text-gray-600 mt-1">{job.description}</p>
                              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                <span>Type: {formatEnumName(job.jobType)}</span>
                                <span>Category: {formatEnumName(job.category)}</span>
                                <span>Completed: {job.completedAt ? formatDate(job.completedAt) : 'N/A'}</span>
                              </div>
                              {job.assignedTo && (
                                <div className="mt-2">
                                  <span className="text-sm text-gray-600">
                                    Completed by: <strong>{job.assignedTo.firstName} {job.assignedTo.lastName}</strong>
                                  </span>
                                </div>
                              )}
                              {job.outputDetails && (
                                <div className="mt-2 p-3 bg-blue-50 rounded-md">
                                  <p className="text-sm text-blue-800">
                                    <strong>Output Details:</strong> {job.outputDetails}
                                  </p>
                                </div>
                              )}
                            </div>
                            <div className="ml-4 flex items-center space-x-2">
                              <button
                                onClick={() => router.push(`/jobs/${job.id}`)}
                                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded text-sm font-medium"
                              >
                                Review & Deliver
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'staff' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Staff Members</h3>
                  {staffMembers.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No staff members found</p>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {staffMembers.map((staff) => (
                        <div key={staff.id} className="border border-gray-200 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900">{staff.firstName} {staff.lastName}</h4>
                          <p className="text-gray-600 text-sm">{staff.email}</p>
                          <div className="mt-2">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800">
                              {formatEnumName(staff.role)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Assignment Modal */}
      {showAssignModal && selectedJob && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Assign Job: {selectedJob.title}
              </h3>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assign to Staff Member
                </label>
                <select
                  value={assignmentData.staffId}
                  onChange={(e) => setAssignmentData({ ...assignmentData, staffId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  required
                >
                  <option value="">Select staff member...</option>
                  {staffMembers.map((staff) => (
                    <option key={staff.id} value={staff.id}>
                      {staff.firstName} {staff.lastName} ({staff.email})
                    </option>
                  ))}
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes for Staff Member (Optional)
                </label>
                <textarea
                  value={assignmentData.notes}
                  onChange={(e) => setAssignmentData({ ...assignmentData, notes: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  rows={3}
                  placeholder="Add any special instructions or notes..."
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowAssignModal(false);
                    setSelectedJob(null);
                    setAssignmentData({ staffId: '', notes: '' });
                  }}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={submitAssignment}
                  disabled={!assignmentData.staffId}
                  className="bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Assign Job
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
