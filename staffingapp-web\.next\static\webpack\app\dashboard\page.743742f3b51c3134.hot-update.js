"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/JobList.tsx":
/*!************************************!*\
  !*** ./src/components/JobList.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JobList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_job__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/job */ \"(app-pages-browser)/./src/lib/job.ts\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(app-pages-browser)/./src/utils/statusDisplay.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Utility function to format enum names with proper spacing\nconst formatEnumName = (enumValue)=>{\n    return enumValue.replace(/([A-Z])/g, \" $1\").trim();\n};\nfunction JobList(param) {\n    let { onError, hoursStatistics } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const fetchJobs = async ()=>{\n            try {\n                console.log(\"JobList: Starting to fetch jobs...\");\n                setIsLoading(true);\n                setError(null);\n                // Check if we're in the browser before making API calls\n                if (false) {}\n                // Add delay to ensure authentication is ready\n                console.log(\"JobList: Waiting for authentication to be ready...\");\n                await new Promise((resolve)=>setTimeout(resolve, 200));\n                // Debug: Check authentication state\n                const token = sessionStorage.getItem(\"token\");\n                const user = sessionStorage.getItem(\"user\");\n                console.log(\"JobList: Auth check - Token exists:\", !!token, \"User exists:\", !!user);\n                console.log(\"JobList: SessionStorage length:\", sessionStorage.length);\n                if (token) {\n                    console.log(\"JobList: Token preview:\", token.substring(0, 20) + \"...\");\n                    console.log(\"JobList: Token length:\", token.length);\n                } else {\n                    console.log(\"JobList: No token found in sessionStorage\");\n                    console.log(\"JobList: All sessionStorage keys:\", Object.keys(sessionStorage));\n                    // Try to get token again after a short delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                    const retryToken = sessionStorage.getItem(\"token\");\n                    console.log(\"JobList: Retry token check:\", !!retryToken);\n                    if (!retryToken) {\n                        console.log(\"JobList: Still no token after retry, user might not be logged in\");\n                        setError(\"Please log in to view jobs\");\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                console.log(\"JobList: Calling getJobs()...\");\n                const jobsData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobs)();\n                console.log(\"JobList: Jobs fetched successfully:\", jobsData);\n                console.log(\"JobList: Number of jobs:\", jobsData.length);\n                setJobs(jobsData);\n            } catch (err) {\n                console.error(\"JobList: Error fetching jobs:\", err);\n                console.error(\"JobList: Error details:\", {\n                    message: err instanceof Error ? err.message : \"Unknown error\",\n                    stack: err instanceof Error ? err.stack : undefined\n                });\n                const errorMessage = err instanceof Error ? err.message : \"Failed to fetch jobs\";\n                setError(errorMessage);\n                onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchJobs();\n    }, [\n        mounted,\n        onError\n    ]);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        fontSize: \"18px\",\n                        fontWeight: \"500\",\n                        margin: \"0 0 16px 0\"\n                    },\n                    children: \"Work History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#6b7280\",\n                        margin: \"0\"\n                    },\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    const getStatusBadge = (status)=>{\n        const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\n        const userRole = user.role || \"User\";\n        const displayText = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getRoleBasedStatusDisplay)(status, userRole);\n        const colors = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getStatusColors)(status);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            style: {\n                display: \"inline-flex\",\n                alignItems: \"center\",\n                padding: \"4px 12px\",\n                borderRadius: \"9999px\",\n                fontSize: \"12px\",\n                fontWeight: \"500\",\n                backgroundColor: colors.bg,\n                color: colors.text,\n                border: \"1px solid \".concat(colors.border)\n            },\n            children: displayText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    height: \"128px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: \"#6b7280\"\n                    },\n                    children: \"Loading jobs...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    borderRadius: \"6px\",\n                    backgroundColor: \"#fef2f2\",\n                    padding: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flexShrink: 0\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                style: {\n                                    height: \"20px\",\n                                    width: \"20px\",\n                                    color: \"#f87171\"\n                                },\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginLeft: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        color: \"#991b1b\",\n                                        margin: \"0 0 8px 0\"\n                                    },\n                                    children: \"Error Loading Jobs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        color: \"#7f1d1d\",\n                                        margin: \"0\"\n                                    },\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    if (jobs.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"500\",\n                            color: \"#111827\",\n                            margin: \"0 0 24px 0\"\n                        },\n                        children: \"Work History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"16px\",\n                            marginBottom: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"Total Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"Pending\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"32px 0\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                style: {\n                                    margin: \"0 auto\",\n                                    height: \"48px\",\n                                    width: \"48px\",\n                                    color: \"#9ca3af\"\n                                },\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    color: \"#111827\",\n                                    margin: \"8px 0\"\n                                },\n                                children: \"No jobs found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginTop: \"4px\",\n                                    fontSize: \"14px\",\n                                    color: \"#6b7280\",\n                                    margin: \"4px 0 24px 0\"\n                                },\n                                children: hoursStatistics && hoursStatistics.hoursAvailable <= 0 ? \"Purchase hours to start submitting jobs.\" : \"Get started by submitting your first job.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            hoursStatistics && hoursStatistics.hoursAvailable > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/submit-job\",\n                                    style: {\n                                        display: \"inline-flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 16px\",\n                                        border: \"none\",\n                                        boxShadow: \"0 1px 2px 0 rgba(0, 0, 0, 0.05)\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        borderRadius: \"6px\",\n                                        color: \"white\",\n                                        backgroundColor: \"#059669\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Submit Job\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    // Calculate summary statistics\n    const totalJobs = jobs.length;\n    const completedJobs = jobs.filter((job)=>job.status === \"Completed\").length;\n    const pendingJobs = jobs.filter((job)=>job.status === \"Pending\").length;\n    const inProgressJobs = jobs.filter((job)=>job.status === \"InProgress\").length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: \"white\",\n            boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n            borderRadius: \"8px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: \"24px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        fontSize: \"18px\",\n                        fontWeight: \"500\",\n                        color: \"#111827\",\n                        margin: \"0 0 24px 0\"\n                    },\n                    children: \"Work History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"grid\",\n                        gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                        gap: \"16px\",\n                        marginBottom: \"24px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"Total Jobs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: totalJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M5 13l4 4L19 7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: completedJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"In Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: inProgressJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"Pending\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: pendingJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        overflow: \"hidden\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        style: {\n                            minWidth: \"100%\",\n                            borderCollapse: \"collapse\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                style: {\n                                    backgroundColor: \"#f9fafb\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Job\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Created\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                style: {\n                                    backgroundColor: \"white\"\n                                },\n                                children: jobs.map((job, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            borderTop: index > 0 ? \"1px solid #e5e7eb\" : \"none\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"14px\",\n                                                                fontWeight: \"500\",\n                                                                color: \"#059669\",\n                                                                cursor: \"pointer\",\n                                                                textDecoration: \"underline\"\n                                                            },\n                                                            onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                            onMouseEnter: (e)=>{\n                                                                e.currentTarget.style.color = \"#047857\";\n                                                            },\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.style.color = \"#059669\";\n                                                            },\n                                                            children: job.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"14px\",\n                                                                color: \"#6b7280\",\n                                                                overflow: \"hidden\",\n                                                                textOverflow: \"ellipsis\",\n                                                                maxWidth: \"300px\"\n                                                            },\n                                                            children: job.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: \"#111827\"\n                                                        },\n                                                        children: formatEnumName(job.jobType)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: \"#6b7280\"\n                                                        },\n                                                        children: formatEnumName(job.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: getStatusBadge(job.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\",\n                                                    fontSize: \"14px\",\n                                                    color: \"#6b7280\"\n                                                },\n                                                children: formatDate(job.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, job.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 404,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n        lineNumber: 399,\n        columnNumber: 5\n    }, this);\n}\n_s(JobList, \"CHYTzYO3MIcSRksHsX403h10Jv0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = JobList;\nvar _c;\n$RefreshReg$(_c, \"JobList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JobList.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: function() { return /* binding */ getRoleBasedStatusDisplay; },\n/* harmony export */   getStatusColors: function() { return /* binding */ getStatusColors; },\n/* harmony export */   getStatusTailwindClasses: function() { return /* binding */ getStatusTailwindClasses; }\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Handle the enum values that might come with hyphens or spaces\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    switch(userRole){\n        case \"User\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"Submitted\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting Supervisor Review\";\n                case \"Released\":\n                    return \"Completed\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Staff\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned to Me\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Supervisor\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Admin\":\n            // Admin sees the raw status names\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        default:\n            return status;\n    }\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9zdGF0dXNEaXNwbGF5LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUVBOztDQUVDLEdBQ00sTUFBTUEsNEJBQTRCLENBQUNDLFFBQWdCQztJQUN4RCxnRUFBZ0U7SUFDaEUsTUFBTUMsbUJBQW1CRixPQUFPRyxPQUFPLENBQUMsVUFBVTtJQUVsRCxPQUFRRjtRQUNOLEtBQUs7WUFDSCxPQUFRQztnQkFDTixLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1Q7b0JBQ0UsT0FBT0Y7WUFDWDtRQUVGLEtBQUs7WUFDSCxPQUFRRTtnQkFDTixLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1Q7b0JBQ0UsT0FBT0Y7WUFDWDtRQUVGLEtBQUs7WUFDSCxPQUFRRTtnQkFDTixLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1Q7b0JBQ0UsT0FBT0Y7WUFDWDtRQUVGLEtBQUs7WUFDSCxrQ0FBa0M7WUFDbEMsT0FBUUU7Z0JBQ04sS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNULEtBQUs7b0JBQ0gsT0FBTztnQkFDVCxLQUFLO29CQUNILE9BQU87Z0JBQ1QsS0FBSztvQkFDSCxPQUFPO2dCQUNUO29CQUNFLE9BQU9GO1lBQ1g7UUFFRjtZQUNFLE9BQU9BO0lBQ1g7QUFDRixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNSSxrQkFBa0IsQ0FBQ0o7SUFDOUIsTUFBTUUsbUJBQW1CRixPQUFPRyxPQUFPLENBQUMsVUFBVTtJQUVsRCxNQUFNRSxlQUFnRjtRQUNwRixPQUFPO1lBQUVDLElBQUk7WUFBV0MsTUFBTTtZQUFXQyxRQUFRO1FBQVU7UUFDM0QsWUFBWTtZQUFFRixJQUFJO1lBQVdDLE1BQU07WUFBV0MsUUFBUTtRQUFVO1FBQ2hFLFlBQVk7WUFBRUYsSUFBSTtZQUFXQyxNQUFNO1lBQVdDLFFBQVE7UUFBVTtRQUNoRSxjQUFjO1lBQUVGLElBQUk7WUFBV0MsTUFBTTtZQUFXQyxRQUFRO1FBQVU7UUFDbEUsa0JBQWtCO1lBQUVGLElBQUk7WUFBV0MsTUFBTTtZQUFXQyxRQUFRO1FBQVU7UUFDdEUsWUFBWTtZQUFFRixJQUFJO1lBQVdDLE1BQU07WUFBV0MsUUFBUTtRQUFVO1FBQ2hFLFVBQVU7WUFBRUYsSUFBSTtZQUFXQyxNQUFNO1lBQVdDLFFBQVE7UUFBVTtJQUNoRTtJQUVBLE9BQU9ILFlBQVksQ0FBQ0gsaUJBQWlCLElBQUk7UUFBRUksSUFBSTtRQUFXQyxNQUFNO1FBQVdDLFFBQVE7SUFBVTtBQUMvRixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNQywyQkFBMkIsQ0FBQ1Q7SUFDdkMsTUFBTUUsbUJBQW1CRixPQUFPRyxPQUFPLENBQUMsVUFBVTtJQUVsRCxNQUFNTyxnQkFBMkM7UUFDL0MsT0FBTztRQUNQLFlBQVk7UUFDWixZQUFZO1FBQ1osY0FBYztRQUNkLGtCQUFrQjtRQUNsQixZQUFZO1FBQ1osVUFBVTtJQUNaO0lBRUEsT0FBT0EsYUFBYSxDQUFDUixpQkFBaUIsSUFBSTtBQUM1QyxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy91dGlscy9zdGF0dXNEaXNwbGF5LnRzPzk1YjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSm9iU3RhdHVzLCBVc2VyUm9sZSB9IGZyb20gJ0AvdHlwZXMvbW9kZWxzJztcblxuLyoqXG4gKiBNYXBzIGpvYiBzdGF0dXNlcyB0byByb2xlLXNwZWNpZmljIGRpc3BsYXkgbmFtZXNcbiAqL1xuZXhwb3J0IGNvbnN0IGdldFJvbGVCYXNlZFN0YXR1c0Rpc3BsYXkgPSAoc3RhdHVzOiBzdHJpbmcsIHVzZXJSb2xlOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICAvLyBIYW5kbGUgdGhlIGVudW0gdmFsdWVzIHRoYXQgbWlnaHQgY29tZSB3aXRoIGh5cGhlbnMgb3Igc3BhY2VzXG4gIGNvbnN0IG5vcm1hbGl6ZWRTdGF0dXMgPSBzdGF0dXMucmVwbGFjZSgvWy1cXHNdL2csICcnKTtcbiAgXG4gIHN3aXRjaCAodXNlclJvbGUpIHtcbiAgICBjYXNlICdVc2VyJzogLy8gQ2xpZW50IHJvbGVcbiAgICAgIHN3aXRjaCAobm9ybWFsaXplZFN0YXR1cykge1xuICAgICAgICBjYXNlICdOZXcnOlxuICAgICAgICAgIHJldHVybiAnU3VibWl0dGVkJztcbiAgICAgICAgY2FzZSAnUmV0dXJuZWQnOlxuICAgICAgICAgIHJldHVybiAnUmV0dXJuZWQnO1xuICAgICAgICBjYXNlICdBc3NpZ25lZCc6XG4gICAgICAgICAgcmV0dXJuICdBc3NpZ25lZCc7XG4gICAgICAgIGNhc2UgJ0luUHJvZ3Jlc3MnOlxuICAgICAgICAgIHJldHVybiAnSW4gUHJvZ3Jlc3MnO1xuICAgICAgICBjYXNlICdBd2FpdGluZ1Jldmlldyc6XG4gICAgICAgICAgcmV0dXJuICdBd2FpdGluZyBTdXBlcnZpc29yIFJldmlldyc7XG4gICAgICAgIGNhc2UgJ1JlbGVhc2VkJzpcbiAgICAgICAgICByZXR1cm4gJ0NvbXBsZXRlZCc7XG4gICAgICAgIGNhc2UgJ0Nsb3NlZCc6XG4gICAgICAgICAgcmV0dXJuICdDbG9zZWQnO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHJldHVybiBzdGF0dXM7XG4gICAgICB9XG4gICAgICBcbiAgICBjYXNlICdTdGFmZic6XG4gICAgICBzd2l0Y2ggKG5vcm1hbGl6ZWRTdGF0dXMpIHtcbiAgICAgICAgY2FzZSAnTmV3JzpcbiAgICAgICAgICByZXR1cm4gJ05ldyc7XG4gICAgICAgIGNhc2UgJ1JldHVybmVkJzpcbiAgICAgICAgICByZXR1cm4gJ1JldHVybmVkJztcbiAgICAgICAgY2FzZSAnQXNzaWduZWQnOlxuICAgICAgICAgIHJldHVybiAnQXNzaWduZWQgdG8gTWUnO1xuICAgICAgICBjYXNlICdJblByb2dyZXNzJzpcbiAgICAgICAgICByZXR1cm4gJ0luIFByb2dyZXNzJztcbiAgICAgICAgY2FzZSAnQXdhaXRpbmdSZXZpZXcnOlxuICAgICAgICAgIHJldHVybiAnQXdhaXRpbmcgUmV2aWV3JztcbiAgICAgICAgY2FzZSAnUmVsZWFzZWQnOlxuICAgICAgICAgIHJldHVybiAnUmVsZWFzZWQnO1xuICAgICAgICBjYXNlICdDbG9zZWQnOlxuICAgICAgICAgIHJldHVybiAnQ2xvc2VkJztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gc3RhdHVzO1xuICAgICAgfVxuICAgICAgXG4gICAgY2FzZSAnU3VwZXJ2aXNvcic6XG4gICAgICBzd2l0Y2ggKG5vcm1hbGl6ZWRTdGF0dXMpIHtcbiAgICAgICAgY2FzZSAnTmV3JzpcbiAgICAgICAgICByZXR1cm4gJ05ldyc7XG4gICAgICAgIGNhc2UgJ1JldHVybmVkJzpcbiAgICAgICAgICByZXR1cm4gJ1JldHVybmVkJztcbiAgICAgICAgY2FzZSAnQXNzaWduZWQnOlxuICAgICAgICAgIHJldHVybiAnQXNzaWduZWQnO1xuICAgICAgICBjYXNlICdJblByb2dyZXNzJzpcbiAgICAgICAgICByZXR1cm4gJ0luIFByb2dyZXNzJztcbiAgICAgICAgY2FzZSAnQXdhaXRpbmdSZXZpZXcnOlxuICAgICAgICAgIHJldHVybiAnQXdhaXRpbmcgUmV2aWV3JztcbiAgICAgICAgY2FzZSAnUmVsZWFzZWQnOlxuICAgICAgICAgIHJldHVybiAnUmVsZWFzZWQnO1xuICAgICAgICBjYXNlICdDbG9zZWQnOlxuICAgICAgICAgIHJldHVybiAnQ2xvc2VkJztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gc3RhdHVzO1xuICAgICAgfVxuICAgICAgXG4gICAgY2FzZSAnQWRtaW4nOlxuICAgICAgLy8gQWRtaW4gc2VlcyB0aGUgcmF3IHN0YXR1cyBuYW1lc1xuICAgICAgc3dpdGNoIChub3JtYWxpemVkU3RhdHVzKSB7XG4gICAgICAgIGNhc2UgJ05ldyc6XG4gICAgICAgICAgcmV0dXJuICdOZXcnO1xuICAgICAgICBjYXNlICdSZXR1cm5lZCc6XG4gICAgICAgICAgcmV0dXJuICdSZXR1cm5lZCc7XG4gICAgICAgIGNhc2UgJ0Fzc2lnbmVkJzpcbiAgICAgICAgICByZXR1cm4gJ0Fzc2lnbmVkJztcbiAgICAgICAgY2FzZSAnSW5Qcm9ncmVzcyc6XG4gICAgICAgICAgcmV0dXJuICdJbiBQcm9ncmVzcyc7XG4gICAgICAgIGNhc2UgJ0F3YWl0aW5nUmV2aWV3JzpcbiAgICAgICAgICByZXR1cm4gJ0F3YWl0aW5nIFJldmlldyc7XG4gICAgICAgIGNhc2UgJ1JlbGVhc2VkJzpcbiAgICAgICAgICByZXR1cm4gJ1JlbGVhc2VkJztcbiAgICAgICAgY2FzZSAnQ2xvc2VkJzpcbiAgICAgICAgICByZXR1cm4gJ0Nsb3NlZCc7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgcmV0dXJuIHN0YXR1cztcbiAgICAgIH1cbiAgICAgIFxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gc3RhdHVzO1xuICB9XG59O1xuXG4vKipcbiAqIEdldHMgdGhlIGNvbG9yIHNjaGVtZSBmb3IgYSBqb2Igc3RhdHVzIGJhZGdlXG4gKi9cbmV4cG9ydCBjb25zdCBnZXRTdGF0dXNDb2xvcnMgPSAoc3RhdHVzOiBzdHJpbmcpOiB7IGJnOiBzdHJpbmc7IHRleHQ6IHN0cmluZzsgYm9yZGVyOiBzdHJpbmcgfSA9PiB7XG4gIGNvbnN0IG5vcm1hbGl6ZWRTdGF0dXMgPSBzdGF0dXMucmVwbGFjZSgvWy1cXHNdL2csICcnKTtcbiAgXG4gIGNvbnN0IHN0YXR1c0NvbG9yczogeyBba2V5OiBzdHJpbmddOiB7IGJnOiBzdHJpbmc7IHRleHQ6IHN0cmluZzsgYm9yZGVyOiBzdHJpbmcgfSB9ID0ge1xuICAgICdOZXcnOiB7IGJnOiAnI2VjZmRmNScsIHRleHQ6ICcjMDY1ZjQ2JywgYm9yZGVyOiAnI2QxZmFlNScgfSxcbiAgICAnUmV0dXJuZWQnOiB7IGJnOiAnI2ZlZjNjNycsIHRleHQ6ICcjOTI0MDBlJywgYm9yZGVyOiAnI2ZkZTY4YScgfSxcbiAgICAnQXNzaWduZWQnOiB7IGJnOiAnI2VmZjZmZicsIHRleHQ6ICcjMWU0MGFmJywgYm9yZGVyOiAnI2JmZGJmZScgfSxcbiAgICAnSW5Qcm9ncmVzcyc6IHsgYmc6ICcjZjBmZGY0JywgdGV4dDogJyMxNDUzMmQnLCBib3JkZXI6ICcjYmJmN2QwJyB9LFxuICAgICdBd2FpdGluZ1Jldmlldyc6IHsgYmc6ICcjZmVmM2M3JywgdGV4dDogJyM5MjQwMGUnLCBib3JkZXI6ICcjZmRlNjhhJyB9LFxuICAgICdSZWxlYXNlZCc6IHsgYmc6ICcjZTBlN2ZmJywgdGV4dDogJyMzNzMwYTMnLCBib3JkZXI6ICcjYzdkMmZlJyB9LFxuICAgICdDbG9zZWQnOiB7IGJnOiAnI2RjZmNlNycsIHRleHQ6ICcjMTY2NTM0JywgYm9yZGVyOiAnI2JiZjdkMCcgfVxuICB9O1xuXG4gIHJldHVybiBzdGF0dXNDb2xvcnNbbm9ybWFsaXplZFN0YXR1c10gfHwgeyBiZzogJyNmM2Y0ZjYnLCB0ZXh0OiAnIzRiNTU2MycsIGJvcmRlcjogJyNkMWQ1ZGInIH07XG59O1xuXG4vKipcbiAqIEdldHMgVGFpbHdpbmQgQ1NTIGNsYXNzZXMgZm9yIHN0YXR1cyBiYWRnZXNcbiAqL1xuZXhwb3J0IGNvbnN0IGdldFN0YXR1c1RhaWx3aW5kQ2xhc3NlcyA9IChzdGF0dXM6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIGNvbnN0IG5vcm1hbGl6ZWRTdGF0dXMgPSBzdGF0dXMucmVwbGFjZSgvWy1cXHNdL2csICcnKTtcbiAgXG4gIGNvbnN0IHN0YXR1c0NsYXNzZXM6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH0gPSB7XG4gICAgJ05ldyc6ICdiZy1lbWVyYWxkLTEwMCB0ZXh0LWVtZXJhbGQtODAwJyxcbiAgICAnUmV0dXJuZWQnOiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnLFxuICAgICdBc3NpZ25lZCc6ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJyxcbiAgICAnSW5Qcm9ncmVzcyc6ICdiZy1lbWVyYWxkLTEwMCB0ZXh0LWVtZXJhbGQtODAwJyxcbiAgICAnQXdhaXRpbmdSZXZpZXcnOiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnLFxuICAgICdSZWxlYXNlZCc6ICdiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTgwMCcsXG4gICAgJ0Nsb3NlZCc6ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gIH07XG5cbiAgcmV0dXJuIHN0YXR1c0NsYXNzZXNbbm9ybWFsaXplZFN0YXR1c10gfHwgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xufTtcbiJdLCJuYW1lcyI6WyJnZXRSb2xlQmFzZWRTdGF0dXNEaXNwbGF5Iiwic3RhdHVzIiwidXNlclJvbGUiLCJub3JtYWxpemVkU3RhdHVzIiwicmVwbGFjZSIsImdldFN0YXR1c0NvbG9ycyIsInN0YXR1c0NvbG9ycyIsImJnIiwidGV4dCIsImJvcmRlciIsImdldFN0YXR1c1RhaWx3aW5kQ2xhc3NlcyIsInN0YXR1c0NsYXNzZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/statusDisplay.ts\n"));

/***/ })

});