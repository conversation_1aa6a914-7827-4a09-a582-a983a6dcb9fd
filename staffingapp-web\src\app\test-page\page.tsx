export default function TestPage() {
  return (
    <div className="p-8">
      <h1 className="text-4xl font-bold text-red-600 mb-8">🚨 TEST PAGE 🚨</h1>
      
      <div className="bg-yellow-200 border-4 border-yellow-600 p-6 rounded-lg mb-8">
        <h2 className="text-2xl font-bold text-yellow-800">This is a simple test page</h2>
        <p className="text-yellow-700 mt-2">If you can see this, Next.js is working correctly.</p>
      </div>

      <div className="bg-blue-100 p-6 rounded-lg">
        <h3 className="text-xl font-semibold text-blue-800 mb-4">Statement of Account Test</h3>
        <div className="bg-white p-4 rounded shadow">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50">
                <th className="p-2 text-left">Date</th>
                <th className="p-2 text-left">Type</th>
                <th className="p-2 text-left">Reference</th>
                <th className="p-2 text-left">Transaction</th>
                <th className="p-2 text-right">Hours Bought</th>
                <th className="p-2 text-right">Hours Spent</th>
                <th className="p-2 text-right">Balance Hours</th>
                <th className="p-2 text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2">2024-01-15</td>
                <td className="p-2">Purchase</td>
                <td className="p-2">INV-001</td>
                <td className="p-2">Hours Purchase</td>
                <td className="p-2 text-right">50.00</td>
                <td className="p-2 text-right">-</td>
                <td className="p-2 text-right">50.00</td>
                <td className="p-2 text-right">$500.00</td>
              </tr>
              <tr className="bg-gray-50">
                <td className="p-2">2024-01-16</td>
                <td className="p-2">Usage</td>
                <td className="p-2">JOB-001</td>
                <td className="p-2">Web Development</td>
                <td className="p-2 text-right">-</td>
                <td className="p-2 text-right">8.00</td>
                <td className="p-2 text-right">42.00</td>
                <td className="p-2 text-right">-$80.00</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
