C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\appsettings.Development.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\appsettings.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\StaffingApp.Api.staticwebassets.endpoints.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\StaffingApp.Api.exe
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\StaffingApp.Api.deps.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\StaffingApp.Api.runtimeconfig.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\StaffingApp.Api.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\StaffingApp.Api.pdb
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\AutoMapper.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\FluentValidation.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\FluentValidation.AspNetCore.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Humanizer.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.AspNetCore.JsonPatch.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Sqlite.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Mono.TextTemplating.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Newtonsoft.Json.Bson.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\SQLitePCLRaw.core.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\SQLitePCLRaw.provider.e_sqlite3.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\System.CodeDom.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\System.Composition.AttributedModel.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\System.Composition.Convention.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\System.Composition.Hosting.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\System.Composition.Runtime.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\System.Composition.TypedParts.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\de\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\es\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\it\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\browser-wasm\nativeassets\net8.0\e_sqlite3.a
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-arm\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-armel\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-mips64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-s390x\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-x64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\linux-x86\native\libe_sqlite3.so
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\win-arm\native\e_sqlite3.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\win-arm64\native\e_sqlite3.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\win-x64\native\e_sqlite3.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\bin\Release\net8.0\runtimes\win-x86\native\e_sqlite3.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.AssemblyInfo.cs
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\scopedcss\bundle\StaffingApp.Api.styles.css
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets.build.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets.development.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets\msbuild.StaffingApp.Api.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets\msbuild.StaffingApp.Api.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets\msbuild.build.StaffingApp.Api.props
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets\msbuild.buildMultiTargeting.StaffingApp.Api.props
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets\msbuild.buildTransitive.StaffingApp.Api.props
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\Staffing.26A11C03.Up2Date
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\refint\StaffingApp.Api.dll
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.pdb
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\StaffingApp.Api.genruntimeconfig.cache
C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-api\src\StaffingApp.Api\obj\Release\net8.0\ref\StaffingApp.Api.dll
