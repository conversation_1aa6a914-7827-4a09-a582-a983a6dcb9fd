using System;
using StaffingApp.Api.Models;

namespace StaffingApp.Api.Models.DTOs
{
    public class HoursPurchaseDTO
    {
        public int Id { get; set; }
        public required string UserId { get; set; }
        public required string UserName { get; set; }
        public decimal Hours { get; set; }
        public decimal Amount { get; set; }
        public required string Currency { get; set; } = "USD";
        public required string PaymentReference { get; set; }
        public required string PaymentProof { get; set; }
        public HoursPurchaseStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ProcessedAt { get; set; }
        public string? ProcessedBy { get; set; }
        public string? Notes { get; set; }
        public required PaymentRateDTO PaymentRate { get; set; }
    }

    public class CreateHoursPurchaseDTO
    {
        public decimal Hours { get; set; }
        public required string PaymentReference { get; set; }
        public required string PaymentProof { get; set; }
    }

    public class PurchaseApprovalDTO
    {
        public required string Notes { get; set; }
    }
}