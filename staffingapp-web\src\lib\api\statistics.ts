import axios from 'axios';
import { HoursStatistics, AccountStatement } from '@/types/models';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export const getHoursStatistics = async (): Promise<HoursStatistics> => {
    // Check if we're in the browser before accessing sessionStorage
    if (typeof window === 'undefined') {
        throw new Error('Authentication required');
    }

    const token = sessionStorage.getItem('token');
    const userStr = sessionStorage.getItem('user');

    if (!token || !userStr) {
        throw new Error('Authentication required');
    }

    const user = JSON.parse(userStr);
    console.log('Fetching statistics for clientId:', user.clientId);

    try {
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        console.log('Request headers:', headers);

        const response = await axios.get(
            `${API_URL}/api/statistics/hours/${user.clientId}`,
            { headers }
        );

        console.log('API Response:', response.data);
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            console.error('API Error:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                clientId: user.clientId,
                url: `${API_URL}/api/statistics/hours/${user.clientId}`
            });

            const status = error.response?.status;
            const errorData = error.response?.data;

            if (status === 401) {
                throw new Error('Authentication required - please log in again');
            } else if (status === 404) {
                throw new Error('Client data not found - please contact support');
            } else if (status === 500) {
                throw new Error('Server error - please try again later');
            } else {
                const message = typeof errorData === 'string' ? errorData :
                               errorData?.message || error.message || 'Unknown error occurred';
                throw new Error(`Failed to fetch statistics: ${message}`);
            }
        } else if (error instanceof Error) {
            throw new Error(`Failed to fetch statistics: ${error.message}`);
        } else {
            throw new Error('Failed to fetch statistics: An unknown error occurred');
        }
    }
};

export const getAccountStatement = async (): Promise<AccountStatement> => {
    // Check if we're in the browser before accessing sessionStorage
    if (typeof window === 'undefined') {
        throw new Error('Authentication required');
    }

    const token = sessionStorage.getItem('token');
    const userStr = sessionStorage.getItem('user');

    if (!token || !userStr) {
        throw new Error('Authentication required');
    }

    const user = JSON.parse(userStr);
    console.log('Fetching account statement for clientId:', user.clientId);

    try {
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        console.log('Request headers:', headers);

        const response = await axios.get(
            `${API_URL}/api/statistics/statement/${user.clientId}`,
            { headers }
        );

        console.log('Account Statement API Response:', response.data);
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            console.error('Account Statement API Error:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                clientId: user.clientId,
                url: `${API_URL}/api/statistics/statement/${user.clientId}`
            });

            const status = error.response?.status;
            const errorData = error.response?.data;

            if (status === 401) {
                throw new Error('Authentication required - please log in again');
            } else if (status === 404) {
                throw new Error('Account statement not found - please contact support');
            } else if (status === 500) {
                throw new Error('Server error - please try again later');
            } else {
                const message = typeof errorData === 'string' ? errorData :
                               errorData?.message || error.message || 'Unknown error occurred';
                throw new Error(`Failed to fetch account statement: ${message}`);
            }
        } else if (error instanceof Error) {
            throw new Error(`Failed to fetch account statement: ${error.message}`);
        } else {
            throw new Error('Failed to fetch account statement: An unknown error occurred');
        }
    }
};