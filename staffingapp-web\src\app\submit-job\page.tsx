'use client';

import { useState, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { JobType, JobCategory, OutputFormat, CreateJobRequest, HoursStatistics } from '@/types/models';
import { getHoursStatistics } from '@/lib/api/statistics';

// Utility function to format enum names with proper spacing
const formatEnumName = (enumValue: string): string => {
  return enumValue.replace(/([A-Z])/g, ' $1').trim();
};

export default function SubmitJobPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hoursLoading, setHoursLoading] = useState(true);
  const [statistics, setStatistics] = useState<HoursStatistics>({
    hoursBought: 0,
    hoursUtilized: 0,
    hoursAvailable: 0,
  });
  const [formData, setFormData] = useState<CreateJobRequest>({
    title: '',
    description: '',
    jobType: JobType.DataEntry,
    category: JobCategory.DataProcessing,
    outputFormat: OutputFormat.PDF,
    attachmentUrl: '',
    referenceUrl: '',
    clientId: 0
  });

  // Fetch hours statistics on component mount
  useEffect(() => {
    const fetchHoursStatistics = async () => {
      try {
        setHoursLoading(true);
        const data = await getHoursStatistics();
        setStatistics(data);
      } catch (err) {
        console.error('Error fetching hours statistics:', err);
        // If authentication error, redirect to login
        if (err instanceof Error && (err.message.includes('Authentication required') || err.message.includes('401'))) {
          router.push('/login');
        }
      } finally {
        setHoursLoading(false);
      }
    };

    fetchHoursStatistics();
  }, [router]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // Check if user has available hours
    if (statistics.hoursAvailable <= 0) {
      setError('You need to purchase hours before submitting a job. Please buy hours first.');
      setIsLoading(false);
      return;
    }

    try {
      const userStr = sessionStorage.getItem('user');
      const token = sessionStorage.getItem('token');

      // Debug: Log what we found in sessionStorage
      console.log('Debug - sessionStorage contents:', {
        hasUser: !!userStr,
        hasToken: !!token,
        userStr: userStr,
        tokenLength: token?.length || 0
      });

      if (!userStr || !token) {
        throw new Error('Authentication required - Please login first');
      }

      const user = JSON.parse(userStr);
      
      const response = await fetch('http://localhost:5000/api/jobs', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          clientId: user.clientId
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Failed to submit job: ${errorData}`);
      }

      // Show success message and redirect
      setError(null);
      // Create a temporary success notification
      const successDiv = document.createElement('div');
      successDiv.className = 'fixed top-4 right-4 bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-lg shadow-lg z-50 flex items-center';
      successDiv.innerHTML = `
        <svg class="h-5 w-5 text-emerald-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">Job submitted successfully! Redirecting to dashboard...</span>
      `;
      document.body.appendChild(successDiv);

      // Remove the notification and redirect after 2 seconds
      setTimeout(() => {
        document.body.removeChild(successDiv);
        router.push('/dashboard');
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit job');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <span className="text-xl font-bold text-emerald-600">Staff Hall</span>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <a
                  href="/dashboard"
                  className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Dashboard
                </a>
                <a
                  href="/submit-job"
                  className="border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Submit Job
                </a>
                <a
                  href="/buy-hours"
                  className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Buy Hours
                </a>
              </div>
            </div>
            <div className="flex items-center">
              <button
                type="button"
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500"
                onClick={() => {
                  sessionStorage.clear();
                  document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                  window.location.href = '/login';
                }}
              >
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="pt-24 py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto">
            <h1 className="text-3xl font-bold mb-8">Submit New Job</h1>

            {/* Hours Warning */}
            {!hoursLoading && statistics.hoursAvailable <= 0 && (
              <div className="bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-emerald-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-emerald-800">No Hours Available</h3>
                    <div className="mt-2 text-sm text-emerald-700">
                      <p>You need to purchase hours before submitting a job. Please buy hours to continue.</p>
                    </div>
                    <div className="mt-4">
                      <a
                        href="/buy-hours"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
                      >
                        Buy Hours Now
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="bg-red-50 p-4 rounded-md mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Error Submitting Job</h3>
                    <div className="mt-2 text-sm text-red-700">{error}</div>
                  </div>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6 bg-white shadow px-6 py-8 rounded-lg">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">Job Title *</label>
                <input
                  type="text"
                  id="title"
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Enter job title"
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description *</label>
                <textarea
                  id="description"
                  required
                  rows={4}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe the job requirements and details"
                />
              </div>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="jobType" className="block text-sm font-medium text-gray-700">Job Type *</label>
                  <select
                    id="jobType"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                    value={formData.jobType}
                    onChange={(e) => setFormData({ ...formData, jobType: e.target.value as JobType })}
                  >
                    {Object.values(JobType).map((type) => (
                      <option key={type} value={type}>{formatEnumName(type)}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700">Category *</label>
                  <select
                    id="category"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value as JobCategory })}
                  >
                    {Object.values(JobCategory).map((category) => (
                      <option key={category} value={category}>{formatEnumName(category)}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="outputFormat" className="block text-sm font-medium text-gray-700">Output Format *</label>
                <select
                  id="outputFormat"
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  value={formData.outputFormat}
                  onChange={(e) => setFormData({ ...formData, outputFormat: e.target.value as OutputFormat })}
                >
                  {Object.values(OutputFormat).map((format) => (
                    <option key={format} value={format}>{formatEnumName(format)}</option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="attachmentUrl" className="block text-sm font-medium text-gray-700">Attachment URL</label>
                  <input
                    type="url"
                    id="attachmentUrl"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                    value={formData.attachmentUrl}
                    onChange={(e) => setFormData({ ...formData, attachmentUrl: e.target.value })}
                    placeholder="https://example.com/attachment.pdf"
                  />
                </div>

                <div>
                  <label htmlFor="referenceUrl" className="block text-sm font-medium text-gray-700">Reference URL</label>
                  <input
                    type="url"
                    id="referenceUrl"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                    value={formData.referenceUrl}
                    onChange={(e) => setFormData({ ...formData, referenceUrl: e.target.value })}
                    placeholder="https://example.com/reference"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => router.push('/dashboard')}
                  className="flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading || hoursLoading || statistics.hoursAvailable <= 0}
                  className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Submitting...' :
                   hoursLoading ? 'Loading...' :
                   statistics.hoursAvailable <= 0 ? 'Buy Hours to Submit Job' : 'Submit Job'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}
