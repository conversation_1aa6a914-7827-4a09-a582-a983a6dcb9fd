import { getRoleBasedStatusDisplay, getStatusColors, getStatusTailwindClasses } from '../statusDisplay';

describe('Role-based Status Display', () => {
  describe('getRoleBasedStatusDisplay', () => {
    it('should display standardized status names for all roles', () => {
      // All roles should see the same status names for now
      const roles = ['User', 'Staff', 'Supervisor', 'Admin'];

      roles.forEach(role => {
        expect(getRoleBasedStatusDisplay('New', role)).toBe('New');
        expect(getRoleBasedStatusDisplay('Returned', role)).toBe('Returned');
        expect(getRoleBasedStatusDisplay('Assigned', role)).toBe('Assigned');
        expect(getRoleBasedStatusDisplay('In Progress', role)).toBe('In Progress');
        expect(getRoleBasedStatusDisplay('Awaiting Review', role)).toBe('Awaiting Review');
        expect(getRoleBasedStatusDisplay('Released', role)).toBe('Released');
        expect(getRoleBasedStatusDisplay('Closed', role)).toBe('Closed');
      });
    });

    it('should handle normalized status names (without hyphens/spaces)', () => {
      expect(getRoleBasedStatusDisplay('InProgress', 'User')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('AwaitingReview', 'User')).toBe('Awaiting Review');
      expect(getRoleBasedStatusDisplay('InProgress', 'Staff')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('AwaitingReview', 'Staff')).toBe('Awaiting Review');
    });

    it('should return original status for unknown roles', () => {
      expect(getRoleBasedStatusDisplay('New', 'UnknownRole')).toBe('New');
      expect(getRoleBasedStatusDisplay('In Progress', 'UnknownRole')).toBe('In Progress');
    });

    it('should return original status for unknown status values', () => {
      expect(getRoleBasedStatusDisplay('UnknownStatus', 'User')).toBe('UnknownStatus');
      expect(getRoleBasedStatusDisplay('UnknownStatus', 'Staff')).toBe('UnknownStatus');
    });
  });

  describe('getStatusColors', () => {
    it('should return correct colors for each status', () => {
      const newColors = getStatusColors('New');
      expect(newColors.bg).toBe('#ecfdf5');
      expect(newColors.text).toBe('#065f46');
      expect(newColors.border).toBe('#d1fae5');

      const inProgressColors = getStatusColors('In Progress');
      expect(inProgressColors.bg).toBe('#f0fdf4');
      expect(inProgressColors.text).toBe('#14532d');
      expect(inProgressColors.border).toBe('#bbf7d0');
    });

    it('should return default colors for unknown status', () => {
      const unknownColors = getStatusColors('UnknownStatus');
      expect(unknownColors.bg).toBe('#f3f4f6');
      expect(unknownColors.text).toBe('#4b5563');
      expect(unknownColors.border).toBe('#d1d5db');
    });
  });

  describe('getStatusTailwindClasses', () => {
    it('should return correct Tailwind classes for each status', () => {
      expect(getStatusTailwindClasses('New')).toBe('bg-emerald-100 text-emerald-800');
      expect(getStatusTailwindClasses('Returned')).toBe('bg-yellow-100 text-yellow-800');
      expect(getStatusTailwindClasses('Assigned')).toBe('bg-blue-100 text-blue-800');
      expect(getStatusTailwindClasses('In Progress')).toBe('bg-emerald-100 text-emerald-800');
      expect(getStatusTailwindClasses('Awaiting Review')).toBe('bg-yellow-100 text-yellow-800');
      expect(getStatusTailwindClasses('Released')).toBe('bg-purple-100 text-purple-800');
      expect(getStatusTailwindClasses('Closed')).toBe('bg-green-100 text-green-800');
    });

    it('should return default classes for unknown status', () => {
      expect(getStatusTailwindClasses('UnknownStatus')).toBe('bg-gray-100 text-gray-800');
    });
  });
});
