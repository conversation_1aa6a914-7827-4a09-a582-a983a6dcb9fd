import { getRoleBasedStatusDisplay, getStatusColors, getStatusTailwindClasses } from '../statusDisplay';

describe('Role-based Status Display', () => {
  describe('getRoleBasedStatusDisplay', () => {
    it('should display correct status names for Client (User) role', () => {
      expect(getRoleBasedStatusDisplay('New', 'User')).toBe('Submitted');
      expect(getRoleBasedStatusDisplay('Returned', 'User')).toBe('Returned');
      expect(getRoleBasedStatusDisplay('Assigned', 'User')).toBe('Assigned');
      expect(getRoleBasedStatusDisplay('In Progress', 'User')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('Awaiting Review', 'User')).toBe('Awaiting Supervisor Review');
      expect(getRoleBasedStatusDisplay('Released', 'User')).toBe('Completed');
      expect(getRoleBasedStatusDisplay('Closed', 'User')).toBe('Closed');
    });

    it('should display correct status names for Supervisor role', () => {
      expect(getRoleBasedStatusDisplay('New', 'Supervisor')).toBe('New');
      expect(getRoleBasedStatusDisplay('Returned', 'Supervisor')).toBe('Returned');
      expect(getRoleBasedStatusDisplay('Assigned', 'Supervisor')).toBe('Assigned');
      expect(getRoleBasedStatusDisplay('In Progress', 'Supervisor')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('Awaiting Review', 'Supervisor')).toBe('Awaiting My Review');
      expect(getRoleBasedStatusDisplay('Released', 'Supervisor')).toBe('Released');
      expect(getRoleBasedStatusDisplay('Closed', 'Supervisor')).toBe('Closed');
    });

    it('should display correct status names for Staff role', () => {
      expect(getRoleBasedStatusDisplay('New', 'Staff')).toBe('New');
      expect(getRoleBasedStatusDisplay('Returned', 'Staff')).toBe('Returned');
      expect(getRoleBasedStatusDisplay('Assigned', 'Staff')).toBe('Assigned to Me');
      expect(getRoleBasedStatusDisplay('In Progress', 'Staff')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('Awaiting Review', 'Staff')).toBe('Awaiting Supervisor Review');
      expect(getRoleBasedStatusDisplay('Released', 'Staff')).toBe('Released');
      expect(getRoleBasedStatusDisplay('Closed', 'Staff')).toBe('Closed');
    });

    it('should display correct status names for Admin role', () => {
      expect(getRoleBasedStatusDisplay('New', 'Admin')).toBe('New');
      expect(getRoleBasedStatusDisplay('Returned', 'Admin')).toBe('Returned');
      expect(getRoleBasedStatusDisplay('Assigned', 'Admin')).toBe('Assigned');
      expect(getRoleBasedStatusDisplay('In Progress', 'Admin')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('Awaiting Review', 'Admin')).toBe('Awaiting Review');
      expect(getRoleBasedStatusDisplay('Released', 'Admin')).toBe('Released');
      expect(getRoleBasedStatusDisplay('Closed', 'Admin')).toBe('Closed');
    });

    it('should handle normalized status names (without hyphens/spaces)', () => {
      expect(getRoleBasedStatusDisplay('InProgress', 'User')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('AwaitingReview', 'User')).toBe('Awaiting Supervisor Review');
      expect(getRoleBasedStatusDisplay('InProgress', 'Staff')).toBe('In Progress');
      expect(getRoleBasedStatusDisplay('AwaitingReview', 'Staff')).toBe('Awaiting Supervisor Review');
      expect(getRoleBasedStatusDisplay('AwaitingReview', 'Supervisor')).toBe('Awaiting My Review');
    });

    it('should return original status for unknown roles', () => {
      expect(getRoleBasedStatusDisplay('New', 'UnknownRole')).toBe('New');
      expect(getRoleBasedStatusDisplay('In Progress', 'UnknownRole')).toBe('In Progress');
    });

    it('should return original status for unknown status values', () => {
      expect(getRoleBasedStatusDisplay('UnknownStatus', 'User')).toBe('UnknownStatus');
      expect(getRoleBasedStatusDisplay('UnknownStatus', 'Staff')).toBe('UnknownStatus');
    });
  });

  describe('getStatusColors', () => {
    it('should return correct colors for each status', () => {
      const newColors = getStatusColors('New');
      expect(newColors.bg).toBe('#ecfdf5');
      expect(newColors.text).toBe('#065f46');
      expect(newColors.border).toBe('#d1fae5');

      const inProgressColors = getStatusColors('In Progress');
      expect(inProgressColors.bg).toBe('#f0fdf4');
      expect(inProgressColors.text).toBe('#14532d');
      expect(inProgressColors.border).toBe('#bbf7d0');
    });

    it('should return default colors for unknown status', () => {
      const unknownColors = getStatusColors('UnknownStatus');
      expect(unknownColors.bg).toBe('#f3f4f6');
      expect(unknownColors.text).toBe('#4b5563');
      expect(unknownColors.border).toBe('#d1d5db');
    });
  });

  describe('getStatusTailwindClasses', () => {
    it('should return correct Tailwind classes for each status', () => {
      expect(getStatusTailwindClasses('New')).toBe('bg-emerald-100 text-emerald-800');
      expect(getStatusTailwindClasses('Returned')).toBe('bg-yellow-100 text-yellow-800');
      expect(getStatusTailwindClasses('Assigned')).toBe('bg-blue-100 text-blue-800');
      expect(getStatusTailwindClasses('In Progress')).toBe('bg-emerald-100 text-emerald-800');
      expect(getStatusTailwindClasses('Awaiting Review')).toBe('bg-yellow-100 text-yellow-800');
      expect(getStatusTailwindClasses('Released')).toBe('bg-purple-100 text-purple-800');
      expect(getStatusTailwindClasses('Closed')).toBe('bg-green-100 text-green-800');
    });

    it('should return default classes for unknown status', () => {
      expect(getStatusTailwindClasses('UnknownStatus')).toBe('bg-gray-100 text-gray-800');
    });
  });
});
