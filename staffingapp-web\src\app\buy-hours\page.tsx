'use client';

import { useState, useEffect, FormEvent } from 'react';
import { useRouter } from 'next/navigation';

interface PaymentRate {
  id: number;
  ratePerHour: number;
  currency: string;
  isActive: boolean;
}

interface PurchaseRequest {
  hours: number;
  paymentReference: string;
  paymentProof: string;
}

interface FileUploadState {
  file: File | null;
  uploading: boolean;
  uploadedUrl: string | null;
}

export default function BuyHoursPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentRate, setCurrentRate] = useState<PaymentRate | null>(null);
  const [formData, setFormData] = useState<PurchaseRequest>({
    hours: 1,
    paymentReference: '',
    paymentProof: ''
  });
  const [fileUpload, setFileUpload] = useState<FileUploadState>({
    file: null,
    uploading: false,
    uploadedUrl: null
  });

  useEffect(() => {
    // Check authentication
    if (typeof window !== 'undefined') {
      const token = sessionStorage.getItem('token');
      if (!token) {
        router.push('/login');
        return;
      }
    }

    // Fetch current payment rate
    fetchCurrentRate();
  }, [router]);

  const fetchCurrentRate = async () => {
    try {
      const token = sessionStorage.getItem('token');
      if (!token) return;

      const response = await fetch('http://localhost:5000/api/hours/rates/current', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const rate = await response.json();
        setCurrentRate(rate);
      } else {
        // If no rate endpoint exists, use a default rate
        setCurrentRate({
          id: 1,
          ratePerHour: 25.00,
          currency: 'USD',
          isActive: true
        });
      }
    } catch (error) {
      console.error('Error fetching rate:', error);
      // Use default rate if API fails
      setCurrentRate({
        id: 1,
        ratePerHour: 25.00,
        currency: 'USD',
        isActive: true
      });
    }
  };

  const calculateTotal = () => {
    if (!currentRate) return 0;
    return formData.hours * currentRate.ratePerHour;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        setError('Please upload an image (JPG, PNG, GIF) or PDF file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB');
        return;
      }

      setFileUpload({
        file,
        uploading: false,
        uploadedUrl: null
      });
      setError(null);
    }
  };

  const uploadFile = async (): Promise<string> => {
    if (!fileUpload.file) {
      throw new Error('No file selected');
    }

    setFileUpload(prev => ({ ...prev, uploading: true }));

    try {
      const token = sessionStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const formData = new FormData();
      formData.append('file', fileUpload.file);
      formData.append('type', 'payment-proof');

      const response = await fetch('http://localhost:5000/api/attachments/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const result = await response.json();
      const uploadedUrl = result.filePath || result.url || `uploads/${fileUpload.file.name}`;

      setFileUpload(prev => ({
        ...prev,
        uploading: false,
        uploadedUrl
      }));

      return uploadedUrl;
    } catch (error) {
      setFileUpload(prev => ({ ...prev, uploading: false }));
      throw error;
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = sessionStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Validate that file is selected
      if (!fileUpload.file && !fileUpload.uploadedUrl) {
        throw new Error('Please select a payment proof file');
      }

      // Upload file if not already uploaded
      let paymentProofUrl = fileUpload.uploadedUrl;
      if (fileUpload.file && !paymentProofUrl) {
        paymentProofUrl = await uploadFile();
      }

      // Submit purchase request
      const purchaseData = {
        ...formData,
        paymentProof: paymentProofUrl
      };

      const response = await fetch('http://localhost:5000/api/HoursPurchase/purchase', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(purchaseData)
      });

      if (!response.ok) {
        console.error('Purchase API Error - Status:', response.status);
        console.error('Purchase API Error - Status Text:', response.statusText);

        let errorMessage = 'Failed to submit purchase request';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (jsonError) {
          console.error('Failed to parse error response as JSON:', jsonError);
          const textResponse = await response.text();
          console.error('Error response text:', textResponse);
          errorMessage = `Server error (${response.status}): ${textResponse || response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      let result;
      try {
        result = await response.json();
      } catch (jsonError) {
        console.error('Failed to parse success response as JSON:', jsonError);
        const textResponse = await response.text();
        console.error('Success response text:', textResponse);
        throw new Error('Server returned invalid response format');
      }
      setSuccess(`Purchase request submitted successfully! Reference ID: ${result.id}`);

      // Reset form
      setFormData({
        hours: 1,
        paymentReference: '',
        paymentProof: ''
      });
      setFileUpload({
        file: null,
        uploading: false,
        uploadedUrl: null
      });

      // Redirect to dashboard after 2 seconds to show the success message briefly
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (err) {
      console.error('Purchase error:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit purchase request');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    sessionStorage.clear();
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    window.location.href = '/login';
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <span className="text-xl font-bold text-emerald-600">Staff Hall</span>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <a
                  href="/dashboard"
                  className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Dashboard
                </a>
                <a
                  href="/submit-job"
                  className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Submit Job
                </a>
                <a
                  href="/buy-hours"
                  className="border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Buy Hours
                </a>
              </div>
            </div>
            <div className="flex items-center">
              <button
                type="button"
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500"
                onClick={handleLogout}
              >
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="pt-24 py-10">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-6">Buy Hours</h1>
              
              {/* Current Rate Display */}
              {currentRate && (
                <div className="bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-emerald-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-emerald-800">Current Rate</h3>
                      <div className="mt-2 text-sm text-emerald-700">
                        <p>${currentRate.ratePerHour.toFixed(2)} {currentRate.currency} per hour</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Error</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Success Message */}
              {success && (
                <div className="bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-emerald-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-emerald-800">Success</h3>
                      <div className="mt-2 text-sm text-emerald-700">
                        <p>{success}</p>
                        <p className="mt-1 text-emerald-600">Redirecting to dashboard in 2 seconds...</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Purchase Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="hours" className="block text-sm font-medium text-gray-700">
                    Number of Hours
                  </label>
                  <input
                    type="number"
                    id="hours"
                    min="1"
                    max="1000"
                    required
                    value={formData.hours}
                    onChange={(e) => setFormData({ ...formData, hours: parseInt(e.target.value) || 1 })}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm"
                  />
                </div>

                {/* Total Cost Display */}
                {currentRate && (
                  <div className="bg-gray-50 rounded-md p-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700">Total Cost:</span>
                      <span className="text-lg font-bold text-gray-900">
                        ${calculateTotal().toFixed(2)} {currentRate.currency}
                      </span>
                    </div>
                  </div>
                )}

                <div>
                  <label htmlFor="paymentReference" className="block text-sm font-medium text-gray-700">
                    Payment Reference
                  </label>
                  <input
                    type="text"
                    id="paymentReference"
                    required
                    placeholder="e.g., Bank transfer reference, PayPal transaction ID"
                    value={formData.paymentReference}
                    onChange={(e) => setFormData({ ...formData, paymentReference: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="paymentProof" className="block text-sm font-medium text-gray-700">
                    Payment Proof
                  </label>
                  <div className="mt-1">
                    <input
                      type="file"
                      id="paymentProof"
                      accept="image/*,.pdf"
                      required
                      onChange={handleFileChange}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100"
                    />
                  </div>

                  {/* File Upload Status */}
                  {fileUpload.file && (
                    <div className="mt-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-green-700">
                          Selected: {fileUpload.file.name} ({(fileUpload.file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                    </div>
                  )}

                  {fileUpload.uploading && (
                    <div className="mt-2 text-sm text-emerald-600">
                      <div className="flex items-center space-x-2">
                        <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Uploading file...</span>
                      </div>
                    </div>
                  )}

                  {fileUpload.uploadedUrl && (
                    <div className="mt-2 text-sm text-emerald-600">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span>File uploaded successfully</span>
                      </div>
                    </div>
                  )}

                  <p className="mt-2 text-sm text-gray-500">
                    Upload a screenshot or document proving your payment (JPG, PNG, GIF, or PDF, max 5MB)
                  </p>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => router.push('/dashboard')}
                    className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading || fileUpload.uploading || (!fileUpload.file && !fileUpload.uploadedUrl)}
                    className="bg-emerald-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
                  >
                    {fileUpload.uploading ? 'Uploading File...' :
                     isLoading ? 'Submitting...' :
                     'Submit Purchase Request'}
                  </button>
                </div>
              </form>

              {/* Instructions */}
              <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Payment Instructions</h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        1. Calculate the total cost based on the current rate<br/>
                        2. Make the payment via your preferred method<br/>
                        3. Fill out this form with payment details<br/>
                        4. Your request will be reviewed and hours will be added to your account
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
