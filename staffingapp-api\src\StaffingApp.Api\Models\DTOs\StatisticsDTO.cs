namespace StaffingApp.Api.Models.DTOs
{
    public class HoursStatisticsDTO
    {
        public decimal HoursBought { get; set; }
        public decimal HoursUtilized { get; set; }
        public decimal HoursAvailable { get; set; }
    }

    public class AccountStatementEntryDTO
    {
        public DateTime Date { get; set; }
        public string Type { get; set; } = string.Empty; // "Purchase", "Usage", "Adjustment"
        public string Reference { get; set; } = string.Empty;
        public string Transaction { get; set; } = string.Empty;
        public decimal HoursBought { get; set; }
        public decimal HoursSpent { get; set; }
        public decimal Adjustment { get; set; }
        public decimal BalanceHours { get; set; }
        public decimal Amount { get; set; }
        public decimal BalanceAmount { get; set; }
    }

    public class AccountStatementDTO
    {
        public List<AccountStatementEntryDTO> Entries { get; set; } = new List<AccountStatementEntryDTO>();
        public decimal TotalHoursBought { get; set; }
        public decimal TotalHoursSpent { get; set; }
        public decimal CurrentHoursBalance { get; set; }
        public decimal TotalAmountSpent { get; set; }
        public decimal CurrentAmountBalance { get; set; }
    }
}