"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: function() { return /* binding */ getRoleBasedStatusDisplay; },\n/* harmony export */   getStatusColors: function() { return /* binding */ getStatusColors; },\n/* harmony export */   getStatusTailwindClasses: function() { return /* binding */ getStatusTailwindClasses; }\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Handle the enum values that might come with hyphens or spaces\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    switch(userRole){\n        case \"User\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"Submitted\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                case \"In-Progress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                case \"Awaiting Review\":\n                    return \"Awaiting Supervisor Review\";\n                case \"Released\":\n                    return \"Completed\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Staff\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned to Me\";\n                case \"InProgress\":\n                case \"In-Progress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                case \"Awaiting Review\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Supervisor\":\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                case \"In-Progress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                case \"Awaiting Review\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        case \"Admin\":\n            // Admin sees the raw status names\n            switch(normalizedStatus){\n                case \"New\":\n                    return \"New\";\n                case \"Returned\":\n                    return \"Returned\";\n                case \"Assigned\":\n                    return \"Assigned\";\n                case \"InProgress\":\n                case \"In-Progress\":\n                    return \"In Progress\";\n                case \"AwaitingReview\":\n                case \"Awaiting Review\":\n                    return \"Awaiting Review\";\n                case \"Released\":\n                    return \"Released\";\n                case \"Closed\":\n                    return \"Closed\";\n                default:\n                    return status;\n            }\n        default:\n            return status;\n    }\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"In-Progress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Awaiting Review\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"In-Progress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Awaiting Review\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/statusDisplay.ts\n"));

/***/ })

});