import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Skip middleware for static files, API routes, and test pages
  if (path.startsWith('/_next') || path.includes('/api/') || path === '/test') {
    return NextResponse.next();
  }

  // Temporarily disable authentication check for dashboard to debug
  if (path.startsWith('/dashboard')) {
    return NextResponse.next();
  }

  const isPublicPath = path === '/login' || path === '/signup';
  const token = request.cookies.get('token');

  // Always allow access to public paths when not authenticated
  if (isPublicPath && !token) {
    return NextResponse.next();
  }

  // Redirect authenticated users away from public paths
  if (isPublicPath && token) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Redirect unauthenticated users to login
  if (!isPublicPath && !token) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/', '/login', '/signup', '/dashboard/:path*']
};