namespace StaffingApp.Api.Models;

public class Attachment
{
    public int Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime UploadedAt { get; set; }
    
    // Optional: If you want to track who uploaded the attachment
    public int? UploadedById { get; set; }
    public User? UploadedBy { get; set; }
}
