/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/jobs/[id]/page";
exports.ids = ["app/jobs/[id]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?4c03":
/*!***********************!*\
  !*** debug (ignored) ***!
  \***********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'jobs',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/jobs/[id]/page.tsx */ \"(rsc)/./src/app/jobs/[id]/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/jobs/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/jobs/[id]/page\",\n        pathname: \"/jobs/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cjobs%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cjobs%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/jobs/[id]/page.tsx */ \"(ssr)/./src/app/jobs/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWUlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q3N0YWZmaW5nYXBwJTVDJTVDc3RhZmZpbmdhcHAtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDam9icyU1QyU1QyU1QmlkJTVEJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUF1SSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWZmaW5nYXBwLXdlYi8/ZTQ2MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhvbWVcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXHN0YWZmaW5nYXBwXFxcXHN0YWZmaW5nYXBwLXdlYlxcXFxzcmNcXFxcYXBwXFxcXGpvYnNcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cjobs%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/jobs/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/jobs/[id]/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_job__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/job */ \"(ssr)/./src/lib/job.ts\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(ssr)/./src/utils/statusDisplay.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction JobDetailsPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [job, setJob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Workflow state\n    const [showCompleteModal, setShowCompleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [outputDetails, setOutputDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const jobId = params.id ? parseInt(params.id) : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!jobId) {\n            setError(\"Invalid job ID\");\n            setIsLoading(false);\n            return;\n        }\n        // Get user info\n        const userStr = sessionStorage.getItem(\"user\");\n        if (userStr) {\n            setUser(JSON.parse(userStr));\n        }\n        const fetchJob = async ()=>{\n            try {\n                console.log(\"JobDetailsPage: Fetching job with ID:\", jobId);\n                const jobData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(jobId);\n                console.log(\"JobDetailsPage: Job fetched successfully:\", jobData);\n                setJob(jobData);\n            } catch (err) {\n                console.error(\"JobDetailsPage: Error fetching job:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Failed to fetch job details\";\n                setError(errorMessage);\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"401\")) {\n                    router.push(\"/login\");\n                }\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchJob();\n    }, [\n        jobId,\n        router\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Workflow action handlers\n    const handleCompleteJob = async ()=>{\n        if (!job || !outputDetails.trim()) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.completeJob)(job.id, outputDetails.trim());\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n            setShowCompleteModal(false);\n            setOutputDetails(\"\");\n        } catch (err) {\n            console.error(\"Error completing job:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to complete job\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleReviewJob = async ()=>{\n        if (!job) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.reviewCompletedJob)(job.id);\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n        } catch (err) {\n            console.error(\"Error reviewing job:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to review job\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleMarkSatisfied = async ()=>{\n        if (!job) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.markJobSatisfied)(job.id);\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n        } catch (err) {\n            console.error(\"Error marking job as satisfied:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to mark job as satisfied\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Determine available actions based on user role and job status\n    const getAvailableActions = ()=>{\n        if (!job || !user) return [];\n        const actions = [];\n        // Staff actions\n        if (user.role === \"Staff\" && job.status === \"InProgress\") {\n            actions.push({\n                type: \"complete\",\n                label: \"Complete Job\",\n                color: \"emerald\",\n                action: ()=>setShowCompleteModal(true)\n            });\n        }\n        // Supervisor actions\n        if (user.role === \"Supervisor\" && job.status === \"AwaitingReview\") {\n            actions.push({\n                type: \"review\",\n                label: \"Review & Deliver\",\n                color: \"blue\",\n                action: handleReviewJob\n            });\n        }\n        // Client actions\n        if (user.role === \"User\" && job.status === \"Released\") {\n            actions.push({\n                type: \"satisfy\",\n                label: \"Mark as Satisfied\",\n                color: \"green\",\n                action: handleMarkSatisfied\n            });\n        }\n        return actions;\n    };\n    const getStatusBadge = (status)=>{\n        const displayText = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getRoleBasedStatusDisplay)(status, user?.role || \"User\");\n        const colors = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getStatusColors)(status);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            style: {\n                backgroundColor: colors.bg,\n                color: colors.text,\n                border: `1px solid ${colors.border}`,\n                padding: \"4px 12px\",\n                borderRadius: \"9999px\",\n                fontSize: \"12px\",\n                fontWeight: \"500\"\n            },\n            children: displayText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-4 text-gray-600\",\n                                        children: \"Loading job details...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-red-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-red-800\",\n                                                children: \"Error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/dashboard\"),\n                                                    className: \"bg-emerald-100 hover:bg-emerald-200 text-emerald-800 px-4 py-2 rounded-md text-sm font-medium\",\n                                                    children: \"Back to Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this);\n    }\n    if (!job) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Job not found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/dashboard\"),\n                                    className: \"mt-4 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Back to Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-emerald-600\",\n                                            children: \"Staff Hall\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/dashboard\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/submit-job\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Submit Job\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/buy-hours\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Buy Hours\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500\",\n                                    onClick: ()=>{\n                                        sessionStorage.clear();\n                                        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n                                        window.location.href = \"/login\";\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/dashboard\"),\n                                    className: \"mb-4 text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: job.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        getStatusBadge(job.status)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow overflow-hidden sm:rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-5 sm:px-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Job Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 max-w-2xl text-sm text-gray-500\",\n                                            children: \"Complete details about this job.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Job ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: [\n                                                            \"#\",\n                                                            job.id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Job Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.jobType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Output Format\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.outputFormat\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: getStatusBadge(job.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            job.updatedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.updatedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.attachmentUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Attachment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: job.attachmentUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-emerald-600 hover:text-emerald-800 underline\",\n                                                            children: \"View Attachment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.referenceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Reference URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: job.referenceUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-emerald-600 hover:text-emerald-800 underline\",\n                                                            children: \"View Reference\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.outputDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Output Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.outputDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.completedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Completed At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.completedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.deliveredAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Delivered At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.deliveredAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.satisfiedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Satisfied At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.satisfiedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        getAvailableActions().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 bg-white shadow sm:rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900 mb-4\",\n                                        children: \"Available Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: getAvailableActions().map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: action.action,\n                                                disabled: isSubmitting,\n                                                className: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${action.color === \"emerald\" ? \"bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500\" : action.color === \"blue\" ? \"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500\" : action.color === \"green\" ? \"bg-green-600 hover:bg-green-700 focus:ring-green-500\" : \"bg-gray-600 hover:bg-gray-700 focus:ring-gray-500\"}`,\n                                                children: isSubmitting ? \"Processing...\" : action.label\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this),\n                        showCompleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: [\n                                                \"Complete Job: \",\n                                                job?.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"outputDetails\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Output Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"outputDetails\",\n                                                    rows: 4,\n                                                    value: outputDetails,\n                                                    onChange: (e)=>setOutputDetails(e.target.value),\n                                                    placeholder: \"Describe how the client can access the output (e.g., 'Document has been emailed to client', 'Files uploaded to shared folder', etc.)\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setShowCompleteModal(false);\n                                                        setOutputDetails(\"\");\n                                                    },\n                                                    className: \"flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500\",\n                                                    disabled: isSubmitting,\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleCompleteJob,\n                                                    disabled: isSubmitting || !outputDetails.trim(),\n                                                    className: \"flex-1 px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isSubmitting ? \"Completing...\" : \"Complete Job\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/jobs/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/job.ts":
/*!************************!*\
  !*** ./src/lib/job.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   completeJob: () => (/* binding */ completeJob),\n/* harmony export */   createJob: () => (/* binding */ createJob),\n/* harmony export */   getJobById: () => (/* binding */ getJobById),\n/* harmony export */   getJobs: () => (/* binding */ getJobs),\n/* harmony export */   markJobSatisfied: () => (/* binding */ markJobSatisfied),\n/* harmony export */   reviewCompletedJob: () => (/* binding */ reviewCompletedJob)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:5000\" || 0;\nconst getJobs = async ()=>{\n    console.log(\"getJobs: Starting...\");\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        console.log(\"getJobs: Not in browser, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    // Add a small delay to ensure sessionStorage is ready\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    const token = sessionStorage.getItem(\"token\");\n    const user = sessionStorage.getItem(\"user\");\n    console.log(\"getJobs: Token exists:\", !!token);\n    console.log(\"getJobs: User exists:\", !!user);\n    console.log(\"getJobs: SessionStorage keys:\", Object.keys(sessionStorage));\n    if (token) {\n        console.log(\"getJobs: Token preview:\", token.substring(0, 20) + \"...\");\n        console.log(\"getJobs: Token length:\", token.length);\n    } else {\n        console.log(\"getJobs: Token is null/undefined/empty\");\n        console.log(\"getJobs: All sessionStorage items:\", {\n            token: sessionStorage.getItem(\"token\"),\n            user: sessionStorage.getItem(\"user\")\n        });\n    }\n    if (!token) {\n        console.log(\"getJobs: No token found, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    console.log(\"getJobs: Making API request to:\", `${API_URL}/api/jobs`);\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/jobs`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        console.log(\"getJobs: Response status:\", response.status);\n        console.log(\"getJobs: Response data type:\", typeof response.data);\n        console.log(\"getJobs: Response data length:\", Array.isArray(response.data) ? response.data.length : \"Not an array\");\n        console.log(\"getJobs: Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"getJobs: API request failed:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"getJobs: Axios error details:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data\n            });\n            if (error.response?.status === 401) {\n                throw new Error(\"Authentication required\");\n            }\n        }\n        throw error;\n    }\n};\nconst getJobById = async (id)=>{\n    console.log(\"getJobById: Starting for job ID:\", id);\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        console.log(\"getJobById: Not in browser, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        console.log(\"getJobById: No token found\");\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        console.log(\"getJobById: Making API request to:\", `${API_URL}/api/jobs/${id}`);\n        console.log(\"getJobById: Using token:\", token.substring(0, 20) + \"...\");\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/jobs/${id}`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        console.log(\"getJobById: Response status:\", response.status);\n        console.log(\"getJobById: Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"getJobById: API request failed:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"getJobById: Axios error details:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data\n            });\n            if (error.response?.status === 401) {\n                throw new Error(\"Authentication required\");\n            }\n            if (error.response?.status === 404) {\n                throw new Error(\"Job not found\");\n            }\n            if (error.response?.status === 403) {\n                throw new Error(\"Access denied\");\n            }\n        }\n        throw error;\n    }\n};\nconst createJob = async (jobData)=>{\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/api/jobs`, jobData, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    return response.data;\n};\n// New workflow API functions\nconst completeJob = async (jobId, outputDetails)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/complete`, {\n        outputDetails\n    }, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\nconst reviewCompletedJob = async (jobId)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/review-completed`, {}, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\nconst markJobSatisfied = async (jobId)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/mark-satisfied`, {}, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2pvYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBRzFCLE1BQU1DLFVBQVVDLHVCQUErQixJQUFJO0FBRTVDLE1BQU1HLFVBQVU7SUFDbkJDLFFBQVFDLEdBQUcsQ0FBQztJQUVaLGdFQUFnRTtJQUNoRSxJQUFJLElBQWtCLEVBQWE7UUFDL0JELFFBQVFDLEdBQUcsQ0FBQztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNwQjtJQUVBLHNEQUFzRDtJQUN0RCxNQUFNLElBQUlDLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFFakQsTUFBTUUsUUFBUUMsZUFBZUMsT0FBTyxDQUFDO0lBQ3JDLE1BQU1DLE9BQU9GLGVBQWVDLE9BQU8sQ0FBQztJQUVwQ1IsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQixDQUFDLENBQUNLO0lBQ3hDTixRQUFRQyxHQUFHLENBQUMseUJBQXlCLENBQUMsQ0FBQ1E7SUFDdkNULFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNTLE9BQU9DLElBQUksQ0FBQ0o7SUFFekQsSUFBSUQsT0FBTztRQUNQTixRQUFRQyxHQUFHLENBQUMsMkJBQTJCSyxNQUFNTSxTQUFTLENBQUMsR0FBRyxNQUFNO1FBQ2hFWixRQUFRQyxHQUFHLENBQUMsMEJBQTBCSyxNQUFNTyxNQUFNO0lBQ3RELE9BQU87UUFDSGIsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0M7WUFDOUNLLE9BQU9DLGVBQWVDLE9BQU8sQ0FBQztZQUM5QkMsTUFBTUYsZUFBZUMsT0FBTyxDQUFDO1FBQ2pDO0lBQ0o7SUFFQSxJQUFJLENBQUNGLE9BQU87UUFDUk4sUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTSxJQUFJQyxNQUFNO0lBQ3BCO0lBRUFGLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUMsQ0FBQyxFQUFFTixRQUFRLFNBQVMsQ0FBQztJQUVwRSxJQUFJO1FBQ0EsTUFBTW1CLFdBQVcsTUFBTXBCLDZDQUFLQSxDQUFDcUIsR0FBRyxDQUFDLENBQUMsRUFBRXBCLFFBQVEsU0FBUyxDQUFDLEVBQUU7WUFDcERxQixTQUFTO2dCQUNMLGlCQUFpQixDQUFDLE9BQU8sRUFBRVYsTUFBTSxDQUFDO2dCQUNsQyxnQkFBZ0I7WUFDcEI7UUFDSjtRQUVBTixRQUFRQyxHQUFHLENBQUMsNkJBQTZCYSxTQUFTRyxNQUFNO1FBQ3hEakIsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQyxPQUFPYSxTQUFTSSxJQUFJO1FBQ2hFbEIsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ2tCLE1BQU1DLE9BQU8sQ0FBQ04sU0FBU0ksSUFBSSxJQUFJSixTQUFTSSxJQUFJLENBQUNMLE1BQU0sR0FBRztRQUNwR2IsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQmEsU0FBU0ksSUFBSTtRQUNwRCxPQUFPSixTQUFTSSxJQUFJO0lBQ3hCLEVBQUUsT0FBT0csT0FBTztRQUNackIsUUFBUXFCLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLElBQUkzQiw2Q0FBS0EsQ0FBQzRCLFlBQVksQ0FBQ0QsUUFBUTtZQUMzQnJCLFFBQVFxQixLQUFLLENBQUMsaUNBQWlDO2dCQUMzQ0osUUFBUUksTUFBTVAsUUFBUSxFQUFFRztnQkFDeEJNLFlBQVlGLE1BQU1QLFFBQVEsRUFBRVM7Z0JBQzVCTCxNQUFNRyxNQUFNUCxRQUFRLEVBQUVJO1lBQzFCO1lBRUEsSUFBSUcsTUFBTVAsUUFBUSxFQUFFRyxXQUFXLEtBQUs7Z0JBQ2hDLE1BQU0sSUFBSWYsTUFBTTtZQUNwQjtRQUNKO1FBQ0EsTUFBTW1CO0lBQ1Y7QUFDSixFQUFFO0FBRUssTUFBTUcsYUFBYSxPQUFPQztJQUM3QnpCLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0N3QjtJQUVoRCxnRUFBZ0U7SUFDaEUsSUFBSSxJQUFrQixFQUFhO1FBQy9CekIsUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTSxJQUFJQyxNQUFNO0lBQ3BCO0lBRUEsTUFBTUksUUFBUUMsZUFBZUMsT0FBTyxDQUFDO0lBQ3JDLElBQUksQ0FBQ0YsT0FBTztRQUNSTixRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNLElBQUlDLE1BQU07SUFDcEI7SUFFQSxJQUFJO1FBQ0FGLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0MsQ0FBQyxFQUFFTixRQUFRLFVBQVUsRUFBRThCLEdBQUcsQ0FBQztRQUM3RXpCLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJLLE1BQU1NLFNBQVMsQ0FBQyxHQUFHLE1BQU07UUFFakUsTUFBTUUsV0FBVyxNQUFNcEIsNkNBQUtBLENBQUNxQixHQUFHLENBQUMsQ0FBQyxFQUFFcEIsUUFBUSxVQUFVLEVBQUU4QixHQUFHLENBQUMsRUFBRTtZQUMxRFQsU0FBUztnQkFDTCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVWLE1BQU0sQ0FBQztnQkFDbEMsZ0JBQWdCO1lBQ3BCO1FBQ0o7UUFFQU4sUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ2EsU0FBU0csTUFBTTtRQUMzRGpCLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJhLFNBQVNJLElBQUk7UUFDdkQsT0FBT0osU0FBU0ksSUFBSTtJQUN4QixFQUFFLE9BQU9HLE9BQU87UUFDWnJCLFFBQVFxQixLQUFLLENBQUMsbUNBQW1DQTtRQUNqRCxJQUFJM0IsNkNBQUtBLENBQUM0QixZQUFZLENBQUNELFFBQVE7WUFDM0JyQixRQUFRcUIsS0FBSyxDQUFDLG9DQUFvQztnQkFDOUNKLFFBQVFJLE1BQU1QLFFBQVEsRUFBRUc7Z0JBQ3hCTSxZQUFZRixNQUFNUCxRQUFRLEVBQUVTO2dCQUM1QkwsTUFBTUcsTUFBTVAsUUFBUSxFQUFFSTtZQUMxQjtZQUVBLElBQUlHLE1BQU1QLFFBQVEsRUFBRUcsV0FBVyxLQUFLO2dCQUNoQyxNQUFNLElBQUlmLE1BQU07WUFDcEI7WUFDQSxJQUFJbUIsTUFBTVAsUUFBUSxFQUFFRyxXQUFXLEtBQUs7Z0JBQ2hDLE1BQU0sSUFBSWYsTUFBTTtZQUNwQjtZQUNBLElBQUltQixNQUFNUCxRQUFRLEVBQUVHLFdBQVcsS0FBSztnQkFDaEMsTUFBTSxJQUFJZixNQUFNO1lBQ3BCO1FBQ0o7UUFDQSxNQUFNbUI7SUFDVjtBQUNKLEVBQUU7QUFFSyxNQUFNSyxZQUFZLE9BQU9DO0lBQzVCLGdFQUFnRTtJQUNoRSxJQUFJLElBQWtCLEVBQWE7UUFDL0IsTUFBTSxJQUFJekIsTUFBTTtJQUNwQjtJQUVBLE1BQU1JLFFBQVFDLGVBQWVDLE9BQU8sQ0FBQztJQUVyQyxJQUFJLENBQUNGLE9BQU87UUFDUixNQUFNLElBQUlKLE1BQU07SUFDcEI7SUFFQSxNQUFNWSxXQUFXLE1BQU1wQiw2Q0FBS0EsQ0FBQ2tDLElBQUksQ0FBQyxDQUFDLEVBQUVqQyxRQUFRLFNBQVMsQ0FBQyxFQUFFZ0MsU0FBUztRQUM5RFgsU0FBUztZQUNMLGlCQUFpQixDQUFDLE9BQU8sRUFBRVYsTUFBTSxDQUFDO1lBQ2xDLGdCQUFnQjtRQUNwQjtJQUNKO0lBRUEsT0FBT1EsU0FBU0ksSUFBSTtBQUN4QixFQUFFO0FBRUYsNkJBQTZCO0FBQ3RCLE1BQU1XLGNBQWMsT0FBT0MsT0FBZUM7SUFDN0MsSUFBSSxJQUFrQixFQUFhO1FBQy9CLE1BQU0sSUFBSTdCLE1BQU07SUFDcEI7SUFFQSxNQUFNSSxRQUFRQyxlQUFlQyxPQUFPLENBQUM7SUFDckMsSUFBSSxDQUFDRixPQUFPO1FBQ1IsTUFBTSxJQUFJSixNQUFNO0lBQ3BCO0lBRUEsTUFBTVIsNkNBQUtBLENBQUNzQyxHQUFHLENBQUMsQ0FBQyxFQUFFckMsUUFBUSxVQUFVLEVBQUVtQyxNQUFNLFNBQVMsQ0FBQyxFQUNuRDtRQUFFQztJQUFjLEdBQ2hCO1FBQ0lmLFNBQVM7WUFDTCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVWLE1BQU0sQ0FBQztZQUNsQyxnQkFBZ0I7UUFDcEI7SUFDSjtBQUVSLEVBQUU7QUFFSyxNQUFNMkIscUJBQXFCLE9BQU9IO0lBQ3JDLElBQUksSUFBa0IsRUFBYTtRQUMvQixNQUFNLElBQUk1QixNQUFNO0lBQ3BCO0lBRUEsTUFBTUksUUFBUUMsZUFBZUMsT0FBTyxDQUFDO0lBQ3JDLElBQUksQ0FBQ0YsT0FBTztRQUNSLE1BQU0sSUFBSUosTUFBTTtJQUNwQjtJQUVBLE1BQU1SLDZDQUFLQSxDQUFDc0MsR0FBRyxDQUFDLENBQUMsRUFBRXJDLFFBQVEsVUFBVSxFQUFFbUMsTUFBTSxpQkFBaUIsQ0FBQyxFQUFFLENBQUMsR0FBRztRQUNqRWQsU0FBUztZQUNMLGlCQUFpQixDQUFDLE9BQU8sRUFBRVYsTUFBTSxDQUFDO1lBQ2xDLGdCQUFnQjtRQUNwQjtJQUNKO0FBQ0osRUFBRTtBQUVLLE1BQU00QixtQkFBbUIsT0FBT0o7SUFDbkMsSUFBSSxJQUFrQixFQUFhO1FBQy9CLE1BQU0sSUFBSTVCLE1BQU07SUFDcEI7SUFFQSxNQUFNSSxRQUFRQyxlQUFlQyxPQUFPLENBQUM7SUFDckMsSUFBSSxDQUFDRixPQUFPO1FBQ1IsTUFBTSxJQUFJSixNQUFNO0lBQ3BCO0lBRUEsTUFBTVIsNkNBQUtBLENBQUNzQyxHQUFHLENBQUMsQ0FBQyxFQUFFckMsUUFBUSxVQUFVLEVBQUVtQyxNQUFNLGVBQWUsQ0FBQyxFQUFFLENBQUMsR0FBRztRQUMvRGQsU0FBUztZQUNMLGlCQUFpQixDQUFDLE9BQU8sRUFBRVYsTUFBTSxDQUFDO1lBQ2xDLGdCQUFnQjtRQUNwQjtJQUNKO0FBQ0osRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWZmaW5nYXBwLXdlYi8uL3NyYy9saWIvam9iLnRzPzY5ODQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJztcclxuaW1wb3J0IHsgSm9iLCBDcmVhdGVKb2JSZXF1ZXN0IH0gZnJvbSAnQC90eXBlcy9tb2RlbHMnO1xyXG5cclxuY29uc3QgQVBJX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMCc7XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0Sm9icyA9IGFzeW5jICgpOiBQcm9taXNlPEpvYltdPiA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnZ2V0Sm9iczogU3RhcnRpbmcuLi4nKTtcclxuXHJcbiAgICAvLyBDaGVjayBpZiB3ZSdyZSBpbiB0aGUgYnJvd3NlciBiZWZvcmUgYWNjZXNzaW5nIHNlc3Npb25TdG9yYWdlXHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnZ2V0Sm9iczogTm90IGluIGJyb3dzZXIsIHRocm93aW5nIGF1dGggZXJyb3InKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQWRkIGEgc21hbGwgZGVsYXkgdG8gZW5zdXJlIHNlc3Npb25TdG9yYWdlIGlzIHJlYWR5XHJcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7XHJcblxyXG4gICAgY29uc3QgdG9rZW4gPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xyXG4gICAgY29uc3QgdXNlciA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3VzZXInKTtcclxuXHJcbiAgICBjb25zb2xlLmxvZygnZ2V0Sm9iczogVG9rZW4gZXhpc3RzOicsICEhdG9rZW4pO1xyXG4gICAgY29uc29sZS5sb2coJ2dldEpvYnM6IFVzZXIgZXhpc3RzOicsICEhdXNlcik7XHJcbiAgICBjb25zb2xlLmxvZygnZ2V0Sm9iczogU2Vzc2lvblN0b3JhZ2Uga2V5czonLCBPYmplY3Qua2V5cyhzZXNzaW9uU3RvcmFnZSkpO1xyXG5cclxuICAgIGlmICh0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdnZXRKb2JzOiBUb2tlbiBwcmV2aWV3OicsIHRva2VuLnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ2dldEpvYnM6IFRva2VuIGxlbmd0aDonLCB0b2tlbi5sZW5ndGgpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnZ2V0Sm9iczogVG9rZW4gaXMgbnVsbC91bmRlZmluZWQvZW1wdHknKTtcclxuICAgICAgICBjb25zb2xlLmxvZygnZ2V0Sm9iczogQWxsIHNlc3Npb25TdG9yYWdlIGl0ZW1zOicsIHtcclxuICAgICAgICAgICAgdG9rZW46IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyksXHJcbiAgICAgICAgICAgIHVzZXI6IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3VzZXInKVxyXG4gICAgICAgIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnZ2V0Sm9iczogTm8gdG9rZW4gZm91bmQsIHRocm93aW5nIGF1dGggZXJyb3InKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ2dldEpvYnM6IE1ha2luZyBBUEkgcmVxdWVzdCB0bzonLCBgJHtBUElfVVJMfS9hcGkvam9ic2ApO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7QVBJX1VSTH0vYXBpL2pvYnNgLCB7XHJcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ2dldEpvYnM6IFJlc3BvbnNlIHN0YXR1czonLCByZXNwb25zZS5zdGF0dXMpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdnZXRKb2JzOiBSZXNwb25zZSBkYXRhIHR5cGU6JywgdHlwZW9mIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdnZXRKb2JzOiBSZXNwb25zZSBkYXRhIGxlbmd0aDonLCBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpID8gcmVzcG9uc2UuZGF0YS5sZW5ndGggOiAnTm90IGFuIGFycmF5Jyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ2dldEpvYnM6IFJlc3BvbnNlIGRhdGE6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldEpvYnM6IEFQSSByZXF1ZXN0IGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgICAgaWYgKGF4aW9zLmlzQXhpb3NFcnJvcihlcnJvcikpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignZ2V0Sm9iczogQXhpb3MgZXJyb3IgZGV0YWlsczonLCB7XHJcbiAgICAgICAgICAgICAgICBzdGF0dXM6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXHJcbiAgICAgICAgICAgICAgICBzdGF0dXNUZXh0OiBlcnJvci5yZXNwb25zZT8uc3RhdHVzVGV4dCxcclxuICAgICAgICAgICAgICAgIGRhdGE6IGVycm9yLnJlc3BvbnNlPy5kYXRhXHJcbiAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldEpvYkJ5SWQgPSBhc3luYyAoaWQ6IG51bWJlcik6IFByb21pc2U8Sm9iPiA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnZ2V0Sm9iQnlJZDogU3RhcnRpbmcgZm9yIGpvYiBJRDonLCBpZCk7XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgd2UncmUgaW4gdGhlIGJyb3dzZXIgYmVmb3JlIGFjY2Vzc2luZyBzZXNzaW9uU3RvcmFnZVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ2dldEpvYkJ5SWQ6IE5vdCBpbiBicm93c2VyLCB0aHJvd2luZyBhdXRoIGVycm9yJyk7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRva2VuID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnZ2V0Sm9iQnlJZDogTm8gdG9rZW4gZm91bmQnKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnZ2V0Sm9iQnlJZDogTWFraW5nIEFQSSByZXF1ZXN0IHRvOicsIGAke0FQSV9VUkx9L2FwaS9qb2JzLyR7aWR9YCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ2dldEpvYkJ5SWQ6IFVzaW5nIHRva2VuOicsIHRva2VuLnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyk7XHJcblxyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KGAke0FQSV9VUkx9L2FwaS9qb2JzLyR7aWR9YCwge1xyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdnZXRKb2JCeUlkOiBSZXNwb25zZSBzdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcclxuICAgICAgICBjb25zb2xlLmxvZygnZ2V0Sm9iQnlJZDogUmVzcG9uc2UgZGF0YTonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignZ2V0Sm9iQnlJZDogQVBJIHJlcXVlc3QgZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgICBpZiAoYXhpb3MuaXNBeGlvc0Vycm9yKGVycm9yKSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdnZXRKb2JCeUlkOiBBeGlvcyBlcnJvciBkZXRhaWxzOicsIHtcclxuICAgICAgICAgICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcclxuICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXNUZXh0LFxyXG4gICAgICAgICAgICAgICAgZGF0YTogZXJyb3IucmVzcG9uc2U/LmRhdGFcclxuICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwNCkge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdKb2Igbm90IGZvdW5kJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMykge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBY2Nlc3MgZGVuaWVkJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgY3JlYXRlSm9iID0gYXN5bmMgKGpvYkRhdGE6IENyZWF0ZUpvYlJlcXVlc3QpOiBQcm9taXNlPEpvYj4gPT4ge1xyXG4gICAgLy8gQ2hlY2sgaWYgd2UncmUgaW4gdGhlIGJyb3dzZXIgYmVmb3JlIGFjY2Vzc2luZyBzZXNzaW9uU3RvcmFnZVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRva2VuID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuXHJcbiAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChgJHtBUElfVVJMfS9hcGkvam9ic2AsIGpvYkRhdGEsIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxufTtcclxuXHJcbi8vIE5ldyB3b3JrZmxvdyBBUEkgZnVuY3Rpb25zXHJcbmV4cG9ydCBjb25zdCBjb21wbGV0ZUpvYiA9IGFzeW5jIChqb2JJZDogbnVtYmVyLCBvdXRwdXREZXRhaWxzOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+ID0+IHtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB0b2tlbiA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XHJcbiAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGF3YWl0IGF4aW9zLnB1dChgJHtBUElfVVJMfS9hcGkvam9icy8ke2pvYklkfS9jb21wbGV0ZWAsXHJcbiAgICAgICAgeyBvdXRwdXREZXRhaWxzIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCByZXZpZXdDb21wbGV0ZWRKb2IgPSBhc3luYyAoam9iSWQ6IG51bWJlcik6IFByb21pc2U8dm9pZD4gPT4ge1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRva2VuID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgYXdhaXQgYXhpb3MucHV0KGAke0FQSV9VUkx9L2FwaS9qb2JzLyR7am9iSWR9L3Jldmlldy1jb21wbGV0ZWRgLCB7fSwge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICAgIH1cclxuICAgIH0pO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IG1hcmtKb2JTYXRpc2ZpZWQgPSBhc3luYyAoam9iSWQ6IG51bWJlcik6IFByb21pc2U8dm9pZD4gPT4ge1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRva2VuID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgYXdhaXQgYXhpb3MucHV0KGAke0FQSV9VUkx9L2FwaS9qb2JzLyR7am9iSWR9L21hcmstc2F0aXNmaWVkYCwge30sIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9XHJcbiAgICB9KTtcclxufTsiXSwibmFtZXMiOlsiYXhpb3MiLCJBUElfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJnZXRKb2JzIiwiY29uc29sZSIsImxvZyIsIkVycm9yIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwidG9rZW4iLCJzZXNzaW9uU3RvcmFnZSIsImdldEl0ZW0iLCJ1c2VyIiwiT2JqZWN0Iiwia2V5cyIsInN1YnN0cmluZyIsImxlbmd0aCIsInJlc3BvbnNlIiwiZ2V0IiwiaGVhZGVycyIsInN0YXR1cyIsImRhdGEiLCJBcnJheSIsImlzQXJyYXkiLCJlcnJvciIsImlzQXhpb3NFcnJvciIsInN0YXR1c1RleHQiLCJnZXRKb2JCeUlkIiwiaWQiLCJjcmVhdGVKb2IiLCJqb2JEYXRhIiwicG9zdCIsImNvbXBsZXRlSm9iIiwiam9iSWQiLCJvdXRwdXREZXRhaWxzIiwicHV0IiwicmV2aWV3Q29tcGxldGVkSm9iIiwibWFya0pvYlNhdGlzZmllZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/job.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: () => (/* binding */ getRoleBasedStatusDisplay),\n/* harmony export */   getStatusColors: () => (/* binding */ getStatusColors),\n/* harmony export */   getStatusTailwindClasses: () => (/* binding */ getStatusTailwindClasses)\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Normalize status to handle both backend enum names and frontend display names\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    let result = status; // Default fallback\n    switch(userRole){\n        case \"User\":\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"Submitted\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"Released\":\n                    result = \"Completed\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Supervisor\":\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting My Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Staff\":\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned to Me\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Admin\":\n            // Admin sees the system status names\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        default:\n            // Default to system status names\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n    }\n    return result;\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/statusDisplay.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"927152f9cf66\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhZmZpbmdhcHAtd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83NzkzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTI3MTUyZjljZjY2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/jobs/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/jobs/[id]/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\jobs\[id]\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staff Hall\",\n    description: \"A modern staffing solution\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTUE7QUFIZ0I7QUFLZixNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULCtKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1N0YWZmIEhhbGwnLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuIHN0YWZmaW5nIHNvbHV0aW9uJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2Zhdmljb24uaWNvP2VhNzAiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();