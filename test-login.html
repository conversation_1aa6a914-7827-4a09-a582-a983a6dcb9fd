<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
</head>
<body>
    <h1>Test Login</h1>
    
    <h2>Test Supervisor Login</h2>
    <button onclick="testSupervisorLogin()">Test Supervisor Login</button>
    <div id="supervisorResult"></div>
    
    <h2>Test Staff Login</h2>
    <button onclick="testStaffLogin()">Test Staff Login</button>
    <div id="staffResult"></div>
    
    <h2>Test Admin Login</h2>
    <button onclick="testAdminLogin()">Test Admin Login</button>
    <div id="adminResult"></div>

    <script>
        async function testSupervisorLogin() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'supervisor123'
                    })
                });
                
                const result = await response.text();
                document.getElementById('supervisorResult').innerHTML = `
                    <p>Status: ${response.status}</p>
                    <p>Response: ${result}</p>
                `;
            } catch (error) {
                document.getElementById('supervisorResult').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        async function testStaffLogin() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'staff123'
                    })
                });
                
                const result = await response.text();
                document.getElementById('staffResult').innerHTML = `
                    <p>Status: ${response.status}</p>
                    <p>Response: ${result}</p>
                `;
            } catch (error) {
                document.getElementById('staffResult').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        async function testAdminLogin() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const result = await response.text();
                document.getElementById('adminResult').innerHTML = `
                    <p>Status: ${response.status}</p>
                    <p>Response: ${result}</p>
                `;
            } catch (error) {
                document.getElementById('adminResult').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
