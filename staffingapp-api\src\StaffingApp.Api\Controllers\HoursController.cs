using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StaffingApp.Api.Data;
using StaffingApp.Api.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace StaffingApp.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HoursController : ControllerBase
    {
        private readonly StaffingContext _context;

        public HoursController(StaffingContext context)
        {
            _context = context;
        }

        [HttpGet("rates")]
        public async Task<ActionResult<PaymentRate>> GetCurrentRate()
        {
            var currentRate = await _context.PaymentRates
                .Where(r => r.IsActive && r.EffectiveFrom <= DateTime.UtcNow && (r.EffectiveTo == null || r.EffectiveTo > DateTime.UtcNow))
                .OrderByDescending(r => r.EffectiveFrom)
                .FirstOrDefaultAsync();

            if (currentRate == null)
                return NotFound("No active payment rate found");

            return Ok(currentRate);
        }

        [HttpPost("rates")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<PaymentRate>> SetRate(PaymentRate rate)
        {
            // Deactivate current active rate
            var currentRate = await _context.PaymentRates
                .Where(r => r.IsActive)
                .ToListAsync();

            foreach (var r in currentRate)
            {
                r.IsActive = false;
                r.EffectiveTo = DateTime.UtcNow;
            }

            // Get the current user ID from the JWT token
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized("Invalid user token");
            }

            // Set new rate
            rate.EffectiveFrom = DateTime.UtcNow;
            rate.IsActive = true;
            rate.CreatedAt = DateTime.UtcNow;
            rate.CreatedById = userId;

            _context.PaymentRates.Add(rate);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetCurrentRate), new { id = rate.Id }, rate);
        }

        [HttpPost("purchase")]
        public async Task<ActionResult<HoursPurchase>> PurchaseHours(HoursPurchase purchase)
        {
            // Get the current user ID from the JWT token
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized("Invalid user token");
            }

            var currentRate = await _context.PaymentRates
                .Where(r => r.IsActive)
                .FirstOrDefaultAsync();

            if (currentRate == null)
                return BadRequest("No active payment rate found");

            purchase.PaymentRateId = currentRate.Id;
            purchase.UserId = userId;
            purchase.Status = HoursPurchaseStatus.Pending;
            purchase.CreatedAt = DateTime.UtcNow;

            _context.HoursPurchases.Add(purchase);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetPurchaseById), new { id = purchase.Id }, purchase);
        }

        [HttpGet("purchases")]
        public async Task<ActionResult<IEnumerable<HoursPurchase>>> GetPurchases()
        {
            var isAdmin = User.IsInRole("Admin");
            var query = _context.HoursPurchases.AsQueryable();

            if (!isAdmin)
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }
                query = query.Where(h => h.UserId == userId);
            }

            query = query
                .Include(h => h.User)
                .Include(h => h.PaymentRate);

            var purchases = await query
                .OrderByDescending(h => h.CreatedAt)
                .ToListAsync();

            return Ok(purchases);
        }

        [HttpGet("purchases/{id}")]
        public async Task<ActionResult<HoursPurchase>> GetPurchaseById(int id)
        {
            var isAdmin = User.IsInRole("Admin");
            var userId = 0;

            if (!isAdmin)
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out userId))
                {
                    return Unauthorized("Invalid user token");
                }
            }

            var purchase = await _context.HoursPurchases
                .Include(h => h.User)
                .Include(h => h.PaymentRate)
                .FirstOrDefaultAsync(h => h.Id == id);

            if (purchase == null)
                return NotFound();

            if (!isAdmin && purchase.UserId != userId)
                return Forbid();

            return Ok(purchase);
        }

        [HttpPut("purchases/{id}/process")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<HoursPurchase>> ProcessPurchase(int id, [FromBody] PurchaseProcessRequest request)
        {
            // Get the current user ID from the JWT token
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized("Invalid user token");
            }

            var purchase = await _context.HoursPurchases
                .Include(p => p.User)
                .FirstOrDefaultAsync(p => p.Id == id);
            if (purchase == null)
                return NotFound();

            if (purchase.Status != HoursPurchaseStatus.Pending)
                return BadRequest("Only pending purchases can be processed");

            purchase.Status = request.Approve ? HoursPurchaseStatus.Approved : HoursPurchaseStatus.Rejected;
            purchase.ProcessedAt = DateTime.UtcNow;
            purchase.ProcessedBy = userId;
            purchase.Notes = request.Notes;

            // If approved, add purchased hours to client's balance
            if (request.Approve)
            {
                var client = await _context.Clients.FirstOrDefaultAsync(c => c.Id == purchase.User.ClientId);
                if (client != null)
                {
                    client.HoursBought += purchase.Hours;
                }
            }

            await _context.SaveChangesAsync();
            return Ok(purchase);
        }
    }

    public class PurchaseProcessRequest
    {
        public bool Approve { get; set; }
        public required string Notes { get; set; }
    }
}