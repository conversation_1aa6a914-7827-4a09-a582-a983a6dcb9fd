﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StaffingApp.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddJobAssignmentFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "Role",
                table: "Users",
                type: "INTEGER",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AddColumn<DateTime>(
                name: "AssignedAt",
                table: "Jobs",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AssignedToId",
                table: "Jobs",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ReviewedAt",
                table: "Jobs",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ReviewedById",
                table: "Jobs",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SupervisorNotes",
                table: "Jobs",
                type: "TEXT",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Jobs_AssignedToId",
                table: "Jobs",
                column: "AssignedToId");

            migrationBuilder.CreateIndex(
                name: "IX_Jobs_ReviewedById",
                table: "Jobs",
                column: "ReviewedById");

            migrationBuilder.AddForeignKey(
                name: "FK_Jobs_Users_AssignedToId",
                table: "Jobs",
                column: "AssignedToId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Jobs_Users_ReviewedById",
                table: "Jobs",
                column: "ReviewedById",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Jobs_Users_AssignedToId",
                table: "Jobs");

            migrationBuilder.DropForeignKey(
                name: "FK_Jobs_Users_ReviewedById",
                table: "Jobs");

            migrationBuilder.DropIndex(
                name: "IX_Jobs_AssignedToId",
                table: "Jobs");

            migrationBuilder.DropIndex(
                name: "IX_Jobs_ReviewedById",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "AssignedAt",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "AssignedToId",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "ReviewedAt",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "ReviewedById",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "SupervisorNotes",
                table: "Jobs");

            migrationBuilder.AlterColumn<string>(
                name: "Role",
                table: "Users",
                type: "TEXT",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "INTEGER");
        }
    }
}
