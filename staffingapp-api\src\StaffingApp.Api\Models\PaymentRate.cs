using System;

namespace StaffingApp.Api.Models
{
    public class PaymentRate
    {
        public int Id { get; set; }
        public decimal RatePerHour { get; set; }
        public required string Currency { get; set; } = "USD";
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedById { get; set; }
        public required User CreatedBy { get; set; }
    }
}