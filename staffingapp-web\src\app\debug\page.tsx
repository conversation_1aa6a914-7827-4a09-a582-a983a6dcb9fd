'use client';

export default function DebugPage() {
  return (
    <div style={{
      margin: 0,
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <div style={{
        padding: '20px',
        margin: '10px 0',
        borderRadius: '8px',
        color: 'white',
        fontSize: '18px',
        fontWeight: 'bold',
        backgroundColor: '#ef4444'
      }}>
        🔴 RED BOX - If you can see this, basic HTML rendering works
      </div>

      <div style={{
        padding: '20px',
        margin: '10px 0',
        borderRadius: '8px',
        color: 'white',
        fontSize: '18px',
        fontWeight: 'bold',
        backgroundColor: '#22c55e'
      }}>
        🟢 GREEN BOX - This is a minimal Next.js page without any complex features
      </div>

      <div style={{
        padding: '20px',
        margin: '10px 0',
        borderRadius: '8px',
        color: 'white',
        fontSize: '18px',
        fontWeight: 'bold',
        backgroundColor: '#3b82f6'
      }}>
        🔵 BLUE BOX - No authentication, no API calls, no complex components
      </div>

      <div style={{
        padding: '20px',
        backgroundColor: 'white',
        borderRadius: '8px',
        marginTop: '20px'
      }}>
        <h2>Debug Information</h2>
        <p>If you can see this page, then:</p>
        <ul>
          <li>✅ Next.js is running</li>
          <li>✅ Basic routing works</li>
          <li>✅ HTML rendering works</li>
        </ul>
        <p>If you can see the colored boxes above, the hydration issue is fixed!</p>
        <div style={{marginTop: '20px'}}>
          <a href="/test" style={{color: '#3b82f6', textDecoration: 'underline'}}>Go to Test Page</a>
          {' | '}
          <a href="/login" style={{color: '#3b82f6', textDecoration: 'underline'}}>Go to Login</a>
          {' | '}
          <a href="/dashboard" style={{color: '#3b82f6', textDecoration: 'underline'}}>Go to Dashboard</a>
        </div>
      </div>
    </div>
  );
}
