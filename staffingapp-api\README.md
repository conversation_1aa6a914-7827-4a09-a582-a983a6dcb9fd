# README.md

# StaffingApp API

## Overview

The StaffingApp API is a .NET 8 minimal API designed to manage staff data for a staffing application. It provides endpoints for adding, updating, and retrieving staff records.

## Project Structure

```
staffingapp-api
├── src
│   ├── Program.cs
│   ├── appsettings.json
│   ├── appsettings.Development.json
│   ├── Properties
│   │   └── launchSettings.json
│   ├── Models
│   │   └── Staff.cs
│   ├── Data
│   │   └── StaffingContext.cs
│   └── Services
│       └── StaffingService.cs
├── tests
│   └── StaffingApp.Tests
│       └── StaffingServiceTests.cs
├── StaffingApp.Api.sln
├── StaffingApp.Api.csproj
└── README.md
```

## Getting Started

1. Clone the repository:
   ```
   git clone <repository-url>
   ```

2. Navigate to the project directory:
   ```
   cd staffingapp-api
   ```

3. Restore the dependencies:
   ```
   dotnet restore
   ```

4. Run the application:
   ```
   dotnet run --project src/StaffingApp.Api.csproj
   ```

## Configuration

The application settings can be configured in the `appsettings.json` and `appsettings.Development.json` files. Make sure to update the connection strings and other settings as needed.

## Testing

Unit tests for the `StaffingService` class can be found in the `tests/StaffingApp.Tests` directory. To run the tests, use the following command:
```
dotnet test
```

## License

This project is licensed under the MIT License. See the LICENSE file for more details.