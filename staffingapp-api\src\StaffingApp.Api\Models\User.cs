namespace StaffingApp.Api.Models;

public class User
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;  // This will store the hashed password
    public string PasswordSalt { get; set; } = string.Empty;
    public string Role { get; set; } = "User";  // Default role is User

    // Foreign key for Client
    public int ClientId { get; set; }
    // Navigation property
    public Client Client { get; set; } = null!;
}
