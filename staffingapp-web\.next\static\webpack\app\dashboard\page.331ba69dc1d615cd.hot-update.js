"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: function() { return /* binding */ getRoleBasedStatusDisplay; },\n/* harmony export */   getStatusColors: function() { return /* binding */ getStatusColors; },\n/* harmony export */   getStatusTailwindClasses: function() { return /* binding */ getStatusTailwindClasses; }\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Debug logging with more detail\n    console.log(\"\\uD83D\\uDD0D getRoleBasedStatusDisplay called:\", {\n        originalStatus: status,\n        userRole,\n        timestamp: new Date().toISOString()\n    });\n    // Normalize status to handle both backend enum names and frontend display names\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    console.log(\"\\uD83D\\uDCDD Normalized status:\", normalizedStatus);\n    let result = status; // Default fallback\n    switch(userRole){\n        case \"User\":\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"Submitted\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"Released\":\n                    result = \"Completed\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Supervisor\":\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting My Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Staff\":\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned to Me\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Admin\":\n            // Admin sees the system status names\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        default:\n            // Default to system status names\n            switch(normalizedStatus){\n                case \"New\":\n                    result = \"New\";\n                    break;\n                case \"Returned\":\n                    result = \"Returned\";\n                    break;\n                case \"Assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"InProgress\":\n                    result = \"In Progress\";\n                    break;\n                case \"AwaitingReview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"Released\":\n                    result = \"Released\";\n                    break;\n                case \"Closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n    }\n    console.log(\"✅ Status display result:\", {\n        originalStatus: status,\n        userRole,\n        result\n    });\n    return result;\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/statusDisplay.ts\n"));

/***/ })

});