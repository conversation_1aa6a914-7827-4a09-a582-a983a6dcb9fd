'use client';

import { useState, useEffect } from 'react';

export default function TestPage() {
  const [currentTime, setCurrentTime] = useState('');

  useEffect(() => {
    setCurrentTime(new Date().toLocaleString());
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-red-500 text-white p-8 rounded-lg mb-8">
          <h1 className="text-3xl font-bold mb-4">🔴 FRONTEND TEST PAGE</h1>
          <p className="text-xl">If you can see this, the Next.js frontend is working correctly!</p>
          {currentTime && <p className="mt-2">Current time: {currentTime}</p>}
        </div>
        
        <div className="bg-blue-500 text-white p-8 rounded-lg mb-8">
          <h2 className="text-2xl font-bold mb-4">🔵 Navigation Test</h2>
          <div className="space-y-2">
            <a href="/login" className="block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
              Go to Login Page
            </a>
            <a href="/dashboard" className="block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
              Go to Dashboard Page
            </a>
            <a href="/submit-job" className="block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
              Go to Submit Job Page
            </a>
          </div>
        </div>
        
        <div className="bg-green-500 text-white p-8 rounded-lg">
          <h2 className="text-2xl font-bold mb-4">🟢 Instructions</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>If you can see this page, the frontend is working</li>
            <li>Click "Go to Login Page" and log in with: <strong><EMAIL></strong> / <strong>password123</strong></li>
            <li>After logging in, click "Go to Dashboard Page"</li>
            <li>You should see the red test box on the dashboard</li>
          </ol>
          <div className="mt-4 p-4 bg-green-600 rounded">
            <h3 className="font-bold">Correct Login Credentials:</h3>
            <p>Email: <code className="bg-green-700 px-2 py-1 rounded"><EMAIL></code></p>
            <p>Password: <code className="bg-green-700 px-2 py-1 rounded">password123</code></p>
          </div>
        </div>
      </div>
    </div>
  );
}
