<!DOCTYPE html>
<html>
<head>
    <title>Create Users</title>
</head>
<body>
    <h1>Create Supervisor and Staff Users</h1>
    
    <h2>Create Supervisor</h2>
    <button onclick="createSupervisor()">Create Supervisor User</button>
    <div id="supervisorResult"></div>
    
    <h2>Create Staff</h2>
    <button onclick="createStaff()">Create Staff User</button>
    <div id="staffResult"></div>
    
    <h2>Check Users</h2>
    <button onclick="checkUsers()">Check All Users</button>
    <div id="usersResult"></div>

    <script>
        async function createSupervisor() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'supervisor123',
                        firstName: 'Sarah',
                        lastName: 'Supervisor',
                        clientId: 2, // Assuming corporate client ID is 2
                        role: 'Supervisor'
                    })
                });
                
                const result = await response.text();
                document.getElementById('supervisorResult').innerHTML = `
                    <p>Status: ${response.status}</p>
                    <p>Response: ${result}</p>
                `;
            } catch (error) {
                document.getElementById('supervisorResult').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        async function createStaff() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'staff123',
                        firstName: 'Mike',
                        lastName: 'Staff',
                        clientId: 2, // Assuming corporate client ID is 2
                        role: 'Staff'
                    })
                });
                
                const result = await response.text();
                document.getElementById('staffResult').innerHTML = `
                    <p>Status: ${response.status}</p>
                    <p>Response: ${result}</p>
                `;
            } catch (error) {
                document.getElementById('staffResult').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        async function checkUsers() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/test-users');
                const result = await response.json();
                document.getElementById('usersResult').innerHTML = `
                    <p>Users found: ${result.length}</p>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('usersResult').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
