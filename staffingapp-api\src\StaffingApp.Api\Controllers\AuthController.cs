using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StaffingApp.Api.Models.DTOs;
using StaffingApp.Api.Services;

namespace StaffingApp.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly AuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(AuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    [HttpPost("login")]
    public async Task<ActionResult<LoginResponseDTO>> Login(LoginDTO loginDto)
    {
        try
        {
            _logger.LogInformation("Login attempt for email: {Email}", loginDto.Email);
            var response = await _authService.LoginAsync(loginDto);

            if (response == null)
            {
                _logger.LogWarning("Login failed for email: {Email}", loginDto.Email);
                return Unauthorized(new { message = "Invalid email or password" });
            }

            _logger.LogInformation("Login successful for email: {Email}", loginDto.Email);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while logging in");
            return StatusCode(500, new { message = "An error occurred while processing your request" });
        }
    }

    [HttpPost("register")]
    public async Task<ActionResult<LoginResponseDTO>> Register(RegisterDTO registerDto)
    {
        try
        {
            _logger.LogInformation("Registration attempt for email: {Email}", registerDto.Email);
            var response = await _authService.RegisterAsync(registerDto);

            if (response == null)
            {
                _logger.LogWarning("Registration failed for email: {Email}", registerDto.Email);
                return BadRequest(new { message = "Registration failed. Email may already be in use." });
            }

            _logger.LogInformation("Registration successful for email: {Email}", registerDto.Email);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while registering user");
            return StatusCode(500, new { message = "An error occurred while processing your request" });
        }
    }

    [HttpGet("debug/users")]
    public async Task<ActionResult> GetUsers()
    {
        try
        {
            var users = await _authService.GetAllUsersAsync();
            return Ok(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users");
            return StatusCode(500, "Error getting users");
        }
    }

    [HttpGet("users/staff")]
    [Authorize(Roles = "Admin,Supervisor")]
    public async Task<ActionResult> GetStaffUsers()
    {
        try
        {
            var staffUsers = await _authService.GetStaffUsersAsync();
            return Ok(staffUsers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting staff users");
            return StatusCode(500, new { message = "An error occurred while processing your request" });
        }
    }

    [HttpPost("seed-roles")]
    public async Task<ActionResult> SeedRoleUsers()
    {
        try
        {
            await _authService.SeedRoleUsersAsync();
            return Ok(new { message = "Role users seeded successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error seeding role users");
            return StatusCode(500, new { message = "An error occurred while seeding users" });
        }
    }

    [HttpGet("test-users")]
    public async Task<ActionResult> TestUsers()
    {
        try
        {
            var users = await _authService.GetAllUsersWithRolesAsync();
            return Ok(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users with roles");
            return StatusCode(500, new { message = "An error occurred while getting users" });
        }
    }

    [HttpPost("create-supervisor")]
    public async Task<ActionResult> CreateSupervisor()
    {
        try
        {
            var result = await _authService.CreateSupervisorUserAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating supervisor user");
            return StatusCode(500, new { message = "An error occurred while creating supervisor user", error = ex.Message });
        }
    }

    [HttpPost("create-staff")]
    public async Task<ActionResult> CreateStaff()
    {
        try
        {
            var result = await _authService.CreateStaffUserAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating staff user");
            return StatusCode(500, new { message = "An error occurred while creating staff user", error = ex.Message });
        }
    }

    [HttpPost("test-login")]
    public async Task<ActionResult> TestLogin([FromBody] LoginDTO loginDto)
    {
        try
        {
            var result = await _authService.LoginAsync(loginDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Test login error for {Email}", loginDto.Email);
            return StatusCode(500, new { message = "Login test failed", error = ex.Message });
        }
    }
}
