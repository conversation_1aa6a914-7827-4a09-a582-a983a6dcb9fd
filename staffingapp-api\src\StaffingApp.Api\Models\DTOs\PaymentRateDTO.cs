using System;

namespace StaffingApp.Api.Models.DTOs
{
    public class PaymentRateDTO
    {
        public int Id { get; set; }
        public decimal RatePerHour { get; set; }
        public required string Currency { get; set; } = "USD";
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public bool IsActive { get; set; }
    }

    public class CreatePaymentRateDTO
    {
        public decimal RatePerHour { get; set; }
        public required string Currency { get; set; } = "USD";
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
    }

    public class UpdatePaymentRateDTO
    {
        public decimal RatePerHour { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public bool IsActive { get; set; }
    }
}