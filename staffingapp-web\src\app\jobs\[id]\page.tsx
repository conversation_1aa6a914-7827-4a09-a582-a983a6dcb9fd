'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Job, UserRole } from '@/types/models';
import { getJobById, completeJob, reviewCompletedJob, markJobSatisfied } from '@/lib/job';

export default function JobDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const [job, setJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);

  // Workflow state
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [outputDetails, setOutputDetails] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const jobId = params.id ? parseInt(params.id as string) : null;

  useEffect(() => {
    if (!jobId) {
      setError('Invalid job ID');
      setIsLoading(false);
      return;
    }

    // Get user info
    const userStr = sessionStorage.getItem('user');
    if (userStr) {
      setUser(JSON.parse(userStr));
    }

    const fetchJob = async () => {
      try {
        console.log('JobDetailsPage: Fetching job with ID:', jobId);
        const jobData = await getJobById(jobId);
        console.log('JobDetailsPage: Job fetched successfully:', jobData);
        setJob(jobData);
      } catch (err) {
        console.error('JobDetailsPage: Error fetching job:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch job details';
        setError(errorMessage);

        if (errorMessage.includes('Authentication required') || errorMessage.includes('401')) {
          router.push('/login');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchJob();
  }, [jobId, router]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Workflow action handlers
  const handleCompleteJob = async () => {
    if (!job || !outputDetails.trim()) return;

    setIsSubmitting(true);
    try {
      await completeJob(job.id, outputDetails.trim());
      // Refresh job data
      const updatedJob = await getJobById(job.id);
      setJob(updatedJob);
      setShowCompleteModal(false);
      setOutputDetails('');
    } catch (err) {
      console.error('Error completing job:', err);
      setError(err instanceof Error ? err.message : 'Failed to complete job');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReviewJob = async () => {
    if (!job) return;

    setIsSubmitting(true);
    try {
      await reviewCompletedJob(job.id);
      // Refresh job data
      const updatedJob = await getJobById(job.id);
      setJob(updatedJob);
    } catch (err) {
      console.error('Error reviewing job:', err);
      setError(err instanceof Error ? err.message : 'Failed to review job');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleMarkSatisfied = async () => {
    if (!job) return;

    setIsSubmitting(true);
    try {
      await markJobSatisfied(job.id);
      // Refresh job data
      const updatedJob = await getJobById(job.id);
      setJob(updatedJob);
    } catch (err) {
      console.error('Error marking job as satisfied:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark job as satisfied');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Determine available actions based on user role and job status
  const getAvailableActions = () => {
    if (!job || !user) return [];

    const actions = [];

    // Staff actions
    if (user.role === 'Staff' && job.status === 'InProgress') {
      actions.push({
        type: 'complete',
        label: 'Complete Job',
        color: 'emerald',
        action: () => setShowCompleteModal(true)
      });
    }

    // Supervisor actions
    if (user.role === 'Supervisor' && job.status === 'Completed') {
      actions.push({
        type: 'review',
        label: 'Review & Deliver',
        color: 'blue',
        action: handleReviewJob
      });
    }

    // Client actions
    if (user.role === 'User' && job.status === 'Delivered') {
      actions.push({
        type: 'satisfy',
        label: 'Mark as Satisfied',
        color: 'green',
        action: handleMarkSatisfied
      });
    }

    return actions;
  };

  const getStatusBadge = (status: string) => {
    const statusColors: { [key: string]: { bg: string; text: string; border: string } } = {
      'Pending': { bg: '#ecfdf5', text: '#065f46', border: '#d1fae5' },
      'InProgress': { bg: '#f0fdf4', text: '#14532d', border: '#bbf7d0' },
      'UnderReview': { bg: '#ecfdf5', text: '#047857', border: '#a7f3d0' },
      'Assigned': { bg: '#eff6ff', text: '#1e40af', border: '#bfdbfe' },
      'Completed': { bg: '#d1fae5', text: '#064e3b', border: '#6ee7b7' },
      'UnderSupervisorReview': { bg: '#fef3c7', text: '#92400e', border: '#fde68a' },
      'Delivered': { bg: '#e0e7ff', text: '#3730a3', border: '#c7d2fe' },
      'Satisfied': { bg: '#dcfce7', text: '#166534', border: '#bbf7d0' },
      'Cancelled': { bg: '#f9fafb', text: '#6b7280', border: '#e5e7eb' },
      'OnHold': { bg: '#f3f4f6', text: '#4b5563', border: '#d1d5db' },
      'Rejected': { bg: '#fee2e2', text: '#991b1b', border: '#fecaca' }
    };

    const colors = statusColors[status] || { bg: '#f3f4f6', text: '#4b5563', border: '#d1d5db' };

    return (
      <span style={{
        backgroundColor: colors.bg,
        color: colors.text,
        border: `1px solid ${colors.border}`,
        padding: '4px 12px',
        borderRadius: '9999px',
        fontSize: '12px',
        fontWeight: '500'
      }}>
        {status}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100">
        {/* Navigation */}
        <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center">
                  <span className="text-xl font-bold text-emerald-600">Staff Hall</span>
                </div>
              </div>
            </div>
          </div>
        </nav>

        <main className="pt-24 py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading job details...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100">
        {/* Navigation */}
        <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center">
                  <span className="text-xl font-bold text-emerald-600">Staff Hall</span>
                </div>
              </div>
            </div>
          </div>
        </nav>

        <main className="pt-24 py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                  <div className="mt-4">
                    <button
                      onClick={() => router.push('/dashboard')}
                      className="bg-emerald-100 hover:bg-emerald-200 text-emerald-800 px-4 py-2 rounded-md text-sm font-medium"
                    >
                      Back to Dashboard
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen bg-gray-100">
        {/* Navigation */}
        <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center">
                  <span className="text-xl font-bold text-emerald-600">Staff Hall</span>
                </div>
              </div>
            </div>
          </div>
        </nav>

        <main className="pt-24 py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <p className="text-gray-600">Job not found</p>
              <button
                onClick={() => router.push('/dashboard')}
                className="mt-4 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <span className="text-xl font-bold text-emerald-600">Staff Hall</span>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <a
                  href="/dashboard"
                  className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Dashboard
                </a>
                <a
                  href="/submit-job"
                  className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Submit Job
                </a>
                <a
                  href="/buy-hours"
                  className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  Buy Hours
                </a>
              </div>
            </div>
            <div className="flex items-center">
              <button
                type="button"
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500"
                onClick={() => {
                  sessionStorage.clear();
                  document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                  window.location.href = '/login';
                }}
              >
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="pt-24 py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/dashboard')}
              className="mb-4 text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Dashboard
            </button>
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold text-gray-900">{job.title}</h1>
              {getStatusBadge(job.status)}
            </div>
          </div>

          {/* Job Details Card */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Job Information</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">Complete details about this job.</p>
            </div>
            <div className="border-t border-gray-200">
              <dl>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Job ID</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">#{job.id}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{job.description}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Job Type</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{job.jobType}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Category</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{job.category}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Output Format</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{job.outputFormat}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{getStatusBadge(job.status)}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(job.createdAt)}</dd>
                </div>
                {job.updatedAt && (
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(job.updatedAt)}</dd>
                  </div>
                )}
                {job.attachmentUrl && (
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Attachment</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <a
                        href={job.attachmentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-emerald-600 hover:text-emerald-800 underline"
                      >
                        View Attachment
                      </a>
                    </dd>
                  </div>
                )}
                {job.referenceUrl && (
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Reference URL</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <a
                        href={job.referenceUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-emerald-600 hover:text-emerald-800 underline"
                      >
                        View Reference
                      </a>
                    </dd>
                  </div>
                )}

                {/* Workflow Information */}
                {job.outputDetails && (
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Output Details</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{job.outputDetails}</dd>
                  </div>
                )}
                {job.completedAt && (
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Completed At</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(job.completedAt)}</dd>
                  </div>
                )}
                {job.deliveredAt && (
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Delivered At</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(job.deliveredAt)}</dd>
                  </div>
                )}
                {job.satisfiedAt && (
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Satisfied At</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(job.satisfiedAt)}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Workflow Actions */}
          {getAvailableActions().length > 0 && (
            <div className="mt-6 bg-white shadow sm:rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Available Actions</h3>
                <div className="flex gap-3">
                  {getAvailableActions().map((action, index) => (
                    <button
                      key={index}
                      onClick={action.action}
                      disabled={isSubmitting}
                      className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
                        action.color === 'emerald' ? 'bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500' :
                        action.color === 'blue' ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500' :
                        action.color === 'green' ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500' :
                        'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500'
                      }`}
                    >
                      {isSubmitting ? 'Processing...' : action.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Complete Job Modal */}
          {showCompleteModal && (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
              <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Complete Job: {job?.title}
                  </h3>
                  <div className="mb-4">
                    <label htmlFor="outputDetails" className="block text-sm font-medium text-gray-700 mb-2">
                      Output Details
                    </label>
                    <textarea
                      id="outputDetails"
                      rows={4}
                      value={outputDetails}
                      onChange={(e) => setOutputDetails(e.target.value)}
                      placeholder="Describe how the client can access the output (e.g., 'Document has been emailed to client', 'Files uploaded to shared folder', etc.)"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                  <div className="flex gap-3">
                    <button
                      onClick={() => {
                        setShowCompleteModal(false);
                        setOutputDetails('');
                      }}
                      className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                      disabled={isSubmitting}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleCompleteJob}
                      disabled={isSubmitting || !outputDetails.trim()}
                      className="flex-1 px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? 'Completing...' : 'Complete Job'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
