namespace StaffingApp.Api.Models.DTOs;

public class LoginDTO
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

public class UserDTO
{
    public int Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public int ClientId { get; set; }
    public string Role { get; set; } = "user";
}

public class LoginResponseDTO
{
    public string Token { get; set; } = string.Empty;
    public UserDTO User { get; set; } = null!;
}

public class RegisterDTO
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string ClientType { get; set; } = string.Empty; // "individual" or "corporate"
    public string CompanyName { get; set; } = string.Empty;
    public string Role { get; set; } = "user"; // Default role is "user", can be "admin"
}
