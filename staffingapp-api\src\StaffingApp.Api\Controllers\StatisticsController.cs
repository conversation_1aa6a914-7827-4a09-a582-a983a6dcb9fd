using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StaffingApp.Api.Services;
using System;
using System.Threading.Tasks;

namespace StaffingApp.Api.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/statistics")]
    public class StatisticsController : ControllerBase
    {
        private readonly StaffingService _staffingService;
        private readonly ILogger<StatisticsController> _logger;

        public StatisticsController(StaffingService staffingService, ILogger<StatisticsController> logger)
        {
            _staffingService = staffingService;
            _logger = logger;
        }

        [HttpGet("hours/{clientId}")]
        public async Task<IActionResult> GetHoursStatistics(int clientId)
        {
            try
            {
                _logger.LogInformation("Getting hours statistics for client {ClientId}", clientId);
                var stats = await _staffingService.GetClientHoursStatisticsAsync(clientId);
                return Ok(stats);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Client {ClientId} not found", clientId);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics for client {ClientId}", clientId);
                return StatusCode(500, "An error occurred while retrieving statistics");
            }
        }

        [HttpGet("statement/{clientId}")]
        public async Task<IActionResult> GetAccountStatement(int clientId)
        {
            try
            {
                _logger.LogInformation("Getting account statement for client {ClientId}", clientId);
                var statement = await _staffingService.GetClientAccountStatementAsync(clientId);
                return Ok(statement);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Client {ClientId} not found", clientId);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account statement for client {ClientId}", clientId);
                return StatusCode(500, "An error occurred while retrieving account statement");
            }
        }
    }
}