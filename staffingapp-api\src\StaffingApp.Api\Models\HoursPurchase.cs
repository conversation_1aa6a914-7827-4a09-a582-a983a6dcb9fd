using System;

namespace StaffingApp.Api.Models
{
    public enum HoursPurchaseStatus
    {
        Pending,
        Approved,
        Rejected
    }

    public class HoursPurchase
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public required User User { get; set; }
        public decimal Hours { get; set; }
        public decimal Amount { get; set; }
        public required string Currency { get; set; } = "USD";
        public required string PaymentReference { get; set; }
        public required string PaymentProof { get; set; }  // File path or URL to the payment proof
        public HoursPurchaseStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ProcessedAt { get; set; }
        public int? ProcessedBy { get; set; }
        public User? Processor { get; set; }
        public string? Notes { get; set; }
        public int PaymentRateId { get; set; }
        public required PaymentRate PaymentRate { get; set; }
    }
}