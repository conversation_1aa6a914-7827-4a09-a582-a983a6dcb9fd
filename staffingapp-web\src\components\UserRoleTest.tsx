'use client';

import { useState, useEffect } from 'react';

export default function UserRoleTest() {
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Test users for each role
  const testUsers = {
    client: {
      id: 'test-client-1',
      email: '<EMAIL>',
      name: 'Test Client',
      role: 'User',
      clientType: 'Individual'
    },
    staff: {
      id: 'test-staff-1',
      email: '<EMAIL>',
      name: 'Test Staff',
      role: 'Staff'
    },
    supervisor: {
      id: 'test-supervisor-1',
      email: '<EMAIL>',
      name: 'Test Supervisor',
      role: 'Supervisor'
    },
    admin: {
      id: 'test-admin-1',
      email: '<EMAIL>',
      name: 'Test Admin',
      role: 'Admin'
    }
  };

  useEffect(() => {
    // Get current user from sessionStorage
    const userStr = sessionStorage.getItem('user');
    if (userStr) {
      setCurrentUser(JSON.parse(userStr));
    }
  }, []);

  const switchUser = (userType: keyof typeof testUsers) => {
    const user = testUsers[userType];
    
    // Update sessionStorage
    sessionStorage.setItem('user', JSON.stringify(user));
    sessionStorage.setItem('token', 'test-token-' + userType);
    
    // Update state
    setCurrentUser(user);
    
    // Refresh the page to update all components
    window.location.reload();
  };

  const clearUser = () => {
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('token');
    setCurrentUser(null);
    window.location.reload();
  };

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f8f9fa', 
      margin: '20px', 
      borderRadius: '8px', 
      border: '2px solid #e9ecef' 
    }}>
      <h2 style={{ marginBottom: '20px', fontSize: '18px', fontWeight: 'bold', color: '#495057' }}>
        User Role Testing
      </h2>
      
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: 'white', borderRadius: '6px' }}>
        <h3 style={{ marginBottom: '10px', fontSize: '16px', fontWeight: '600', color: '#212529' }}>
          Current User:
        </h3>
        {currentUser ? (
          <div style={{ fontSize: '14px' }}>
            <div><strong>Name:</strong> {currentUser.name}</div>
            <div><strong>Email:</strong> {currentUser.email}</div>
            <div><strong>Role:</strong> <span style={{ 
              padding: '2px 8px', 
              backgroundColor: '#e3f2fd', 
              color: '#1565c0', 
              borderRadius: '4px',
              fontWeight: '500'
            }}>{currentUser.role}</span></div>
            <div><strong>ID:</strong> {currentUser.id}</div>
          </div>
        ) : (
          <div style={{ color: '#6c757d', fontStyle: 'italic' }}>No user logged in</div>
        )}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ marginBottom: '15px', fontSize: '16px', fontWeight: '600', color: '#212529' }}>
          Switch to Test User:
        </h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={() => switchUser('client')}
            style={{
              padding: '8px 16px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Switch to Client (User)
          </button>
          
          <button
            onClick={() => switchUser('staff')}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Switch to Staff
          </button>
          
          <button
            onClick={() => switchUser('supervisor')}
            style={{
              padding: '8px 16px',
              backgroundColor: '#ffc107',
              color: '#212529',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Switch to Supervisor
          </button>
          
          <button
            onClick={() => switchUser('admin')}
            style={{
              padding: '8px 16px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Switch to Admin
          </button>
          
          <button
            onClick={clearUser}
            style={{
              padding: '8px 16px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Clear User
          </button>
        </div>
      </div>

      <div style={{ 
        padding: '15px', 
        backgroundColor: '#fff3cd', 
        borderRadius: '6px',
        border: '1px solid #ffeaa7'
      }}>
        <h4 style={{ marginBottom: '10px', fontSize: '14px', fontWeight: '600', color: '#856404' }}>
          Expected Status Mappings:
        </h4>
        <div style={{ fontSize: '12px', color: '#856404' }}>
          <div><strong>Client (User):</strong> New → Submitted, AwaitingReview → Awaiting Supervisor Review, Released → Completed</div>
          <div><strong>Staff:</strong> Assigned → Assigned to Me, AwaitingReview → Awaiting Supervisor Review</div>
          <div><strong>Supervisor:</strong> AwaitingReview → Awaiting My Review</div>
          <div><strong>Admin:</strong> Shows system status names</div>
        </div>
      </div>

      <div style={{ 
        marginTop: '15px',
        padding: '10px', 
        backgroundColor: '#d1ecf1', 
        borderRadius: '6px',
        border: '1px solid #bee5eb',
        fontSize: '12px',
        color: '#0c5460'
      }}>
        <strong>Instructions:</strong> Click a role button above to switch users, then check the Work History section 
        and Status Display Test below to verify that status names change based on the selected role.
      </div>
    </div>
  );
}
