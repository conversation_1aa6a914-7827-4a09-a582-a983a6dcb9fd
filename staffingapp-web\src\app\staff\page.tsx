'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Job, User, JobStatus } from '@/types/models';
import { getRoleBasedStatusDisplay, getStatusTailwindClasses } from '@/utils/statusDisplay';

// Utility function to format enum names with proper spacing
const formatEnumName = (enumValue: string): string => {
  return enumValue.replace(/([A-Z])/g, ' $1').trim();
};

export default function StaffDashboard() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [assignedJobs, setAssignedJobs] = useState<Job[]>([]);
  const [activeTab, setActiveTab] = useState('assigned');

  useEffect(() => {
    const checkAuth = () => {
      const token = sessionStorage.getItem('token');
      const userStr = sessionStorage.getItem('user');
      
      if (!token || !userStr) {
        router.push('/login');
        return;
      }

      const userData = JSON.parse(userStr);
      if (userData.role !== 'Staff') {
        router.push('/dashboard');
        return;
      }

      setUser(userData);
      fetchAssignedJobs();
    };

    checkAuth();
  }, [router]);

  const fetchAssignedJobs = async () => {
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/jobs/assigned', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const jobsData = await response.json();
        setAssignedJobs(jobsData);
      }
    } catch (error) {
      console.error('Error fetching assigned jobs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    sessionStorage.clear();
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    window.location.href = '/login';
  };

  const getJobsByStatus = (status: string) => {
    return assignedJobs.filter(job => job.status === status);
  };

  const getStatusColor = (status: string) => {
    return getStatusTailwindClasses(status);
  };

  const getStatusDisplay = (status: string) => {
    return getRoleBasedStatusDisplay(status, 'Staff');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleStartJob = async (jobId: number) => {
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch(`http://localhost:5000/api/jobs/${jobId}/start`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        fetchAssignedJobs(); // Refresh the jobs list
      }
    } catch (error) {
      console.error('Error starting job:', error);
    }
  };

  // Remove the old handleCompleteJob function - jobs should be completed via the job details page
  // where staff can provide output details

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading staff dashboard...</p>
        </div>
      </div>
    );
  }

  const newAssignedJobs = getJobsByStatus('Assigned');
  const inProgressJobs = getJobsByStatus('In-Progress');
  const completedJobs = getJobsByStatus('Awaiting Review');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">Staff Hall - Staff Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                Welcome, {user?.firstName} {user?.lastName}
              </span>
              <button
                onClick={handleLogout}
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="pt-24 py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{newAssignedJobs.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">New Assignments</dt>
                      <dd className="text-lg font-medium text-gray-900">{newAssignedJobs.length} jobs</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{inProgressJobs.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                      <dd className="text-lg font-medium text-gray-900">{inProgressJobs.length} jobs</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{completedJobs.length}</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Completed</dt>
                      <dd className="text-lg font-medium text-gray-900">{completedJobs.length} jobs</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="bg-white shadow rounded-lg">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                {[
                  { id: 'assigned', name: 'New Assignments', count: newAssignedJobs.length },
                  { id: 'progress', name: 'In Progress', count: inProgressJobs.length },
                  { id: 'completed', name: 'Completed', count: completedJobs.length },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-emerald-500 text-emerald-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                  >
                    {tab.name}
                    <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                      {tab.count}
                    </span>
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'assigned' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">New Job Assignments</h3>
                  {newAssignedJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No new job assignments</p>
                  ) : (
                    <div className="space-y-4">
                      {newAssignedJobs.map((job) => (
                        <div key={job.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="text-lg font-medium text-gray-900">{job.title}</h4>
                              <p className="text-gray-600 mt-1">{job.description}</p>
                              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                <span>Type: {formatEnumName(job.jobType)}</span>
                                <span>Category: {formatEnumName(job.category)}</span>
                                <span>Assigned: {job.assignedAt ? formatDate(job.assignedAt) : 'N/A'}</span>
                              </div>
                              {job.supervisorNotes && (
                                <div className="mt-2 p-2 bg-blue-50 rounded">
                                  <p className="text-sm text-blue-800">
                                    <strong>Supervisor Notes:</strong> {job.supervisorNotes}
                                  </p>
                                </div>
                              )}
                            </div>
                            <div className="ml-4 flex space-x-2">
                              <button
                                onClick={() => router.push(`/jobs/${job.id}`)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                              >
                                View Details
                              </button>
                              <button
                                onClick={() => handleStartJob(job.id)}
                                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded text-sm"
                              >
                                Start Working
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'progress' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Jobs In Progress</h3>
                  {inProgressJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No jobs in progress</p>
                  ) : (
                    <div className="space-y-4">
                      {inProgressJobs.map((job) => (
                        <div key={job.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="text-lg font-medium text-gray-900">{job.title}</h4>
                              <p className="text-gray-600 mt-1">{job.description}</p>
                              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                <span>Type: {formatEnumName(job.jobType)}</span>
                                <span>Category: {formatEnumName(job.category)}</span>
                                <span>Started: {job.assignedAt ? formatDate(job.assignedAt) : 'N/A'}</span>
                              </div>
                            </div>
                            <div className="ml-4 flex space-x-2">
                              <button
                                onClick={() => router.push(`/jobs/${job.id}`)}
                                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded text-sm font-medium"
                              >
                                View Details & Complete
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'completed' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Completed Jobs</h3>
                  {completedJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No completed jobs</p>
                  ) : (
                    <div className="space-y-4">
                      {completedJobs.map((job) => (
                        <div key={job.id} className="border border-gray-200 rounded-lg p-4 bg-green-50">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="text-lg font-medium text-gray-900">{job.title}</h4>
                              <p className="text-gray-600 mt-1">{job.description}</p>
                              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                <span>Type: {formatEnumName(job.jobType)}</span>
                                <span>Category: {formatEnumName(job.category)}</span>
                                <span>Completed: {job.updatedAt ? formatDate(job.updatedAt) : 'N/A'}</span>
                              </div>
                            </div>
                            <div className="ml-4 flex items-center space-x-2">
                              <button
                                onClick={() => router.push(`/jobs/${job.id}`)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                              >
                                View Details
                              </button>
                              <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
                                Completed
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
