using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StaffingApp.Api.Data;
using StaffingApp.Api.Models;
using System.Security.Claims;

namespace StaffingApp.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AttachmentsController : ControllerBase
{
    private readonly StaffingContext _context;
    private readonly ILogger<AttachmentsController> _logger;
    private readonly IWebHostEnvironment _environment;

    public AttachmentsController(
        StaffingContext context, 
        ILogger<AttachmentsController> logger,
        IWebHostEnvironment environment)
    {
        _context = context;
        _logger = logger;
        _environment = environment;
    }

    [HttpPost("upload")]
    public async Task<ActionResult<object>> UploadFile(IFormFile file, [FromForm] string? type = null)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "No file uploaded" });
            }

            // Validate file size (max 5MB)
            if (file.Length > 5 * 1024 * 1024)
            {
                return BadRequest(new { message = "File size must be less than 5MB" });
            }

            // Validate file type
            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "application/pdf" };
            if (!allowedTypes.Contains(file.ContentType.ToLower()))
            {
                return BadRequest(new { message = "Only image files (JPG, PNG, GIF) and PDF files are allowed" });
            }

            // Get current user ID
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out int userId))
            {
                return Unauthorized(new { message = "Invalid user" });
            }

            // Create uploads directory if it doesn't exist
            var uploadsPath = Path.Combine(_environment.ContentRootPath, "uploads");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }

            // Generate unique filename
            var fileExtension = Path.GetExtension(file.FileName);
            var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
            var filePath = Path.Combine(uploadsPath, uniqueFileName);

            // Save file to disk
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Save attachment record to database
            var attachment = new Attachment
            {
                FileName = file.FileName,
                FilePath = $"uploads/{uniqueFileName}",
                ContentType = file.ContentType,
                FileSize = file.Length,
                UploadedAt = DateTime.UtcNow,
                UploadedById = userId
            };

            _context.Attachments.Add(attachment);
            await _context.SaveChangesAsync();

            _logger.LogInformation("File uploaded successfully: {FileName} by user {UserId}", file.FileName, userId);

            return Ok(new
            {
                id = attachment.Id,
                fileName = attachment.FileName,
                filePath = attachment.FilePath,
                contentType = attachment.ContentType,
                fileSize = attachment.FileSize,
                uploadedAt = attachment.UploadedAt,
                url = attachment.FilePath
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file");
            return StatusCode(500, new { message = "An error occurred while uploading the file" });
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> GetFile(int id)
    {
        try
        {
            var attachment = await _context.Attachments.FindAsync(id);
            if (attachment == null)
            {
                return NotFound(new { message = "File not found" });
            }

            var filePath = Path.Combine(_environment.ContentRootPath, attachment.FilePath);
            if (!System.IO.File.Exists(filePath))
            {
                return NotFound(new { message = "Physical file not found" });
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
            return File(fileBytes, attachment.ContentType, attachment.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving file {FileId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the file" });
        }
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteFile(int id)
    {
        try
        {
            var attachment = await _context.Attachments.FindAsync(id);
            if (attachment == null)
            {
                return NotFound(new { message = "File not found" });
            }

            // Check if user owns the file or is admin
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out int userId))
            {
                return Unauthorized(new { message = "Invalid user" });
            }

            if (attachment.UploadedById != userId)
            {
                return StatusCode(403, new { message = "You can only delete your own files" });
            }

            // Delete physical file
            var filePath = Path.Combine(_environment.ContentRootPath, attachment.FilePath);
            if (System.IO.File.Exists(filePath))
            {
                System.IO.File.Delete(filePath);
            }

            // Delete database record
            _context.Attachments.Remove(attachment);
            await _context.SaveChangesAsync();

            _logger.LogInformation("File deleted successfully: {FileName} by user {UserId}", attachment.FileName, userId);

            return Ok(new { message = "File deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FileId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting the file" });
        }
    }
}
