# Role-Based Job Status Display System

## Overview

The Staff Hall application now implements a role-based job status display system where the same underlying job status appears differently to different user roles, providing a more intuitive and role-specific user experience.

## Job Status Workflow

The system uses the following core job statuses:

1. **New** - Job submitted by client, waiting for supervisor review
2. **Returned** - Job returned to client for revision
3. **Assigned** - Job assigned to staff member by supervisor
4. **In-Progress** - Job being worked on by staff
5. **Awaiting Review** - Job completed by staff, waiting for supervisor review
6. **Released** - Job reviewed and delivered to client
7. **Closed** - Job marked as satisfied by client

## Role-Based Display Mapping

### Client (User Role)
- `New` → **"Submitted"**
- `Returned` → **"Returned"**
- `Assigned` → **"Assigned"**
- `In Progress` → **"In Progress"**
- `Awaiting Review` → **"Awaiting Supervisor Review"**
- `Released` → **"Completed"**
- `Closed` → **"Closed"**

### Supervisor Role
- `New` → **"New"**
- `Returned` → **"Returned"**
- `Assigned` → **"Assigned"**
- `In Progress` → **"In Progress"**
- `Awaiting Review` → **"Awaiting My Review"**
- `Released` → **"Released"**
- `Closed` → **"Closed"**

### Staff Role
- `New` → **"New"**
- `Returned` → **"Returned"**
- `Assigned` → **"Assigned to Me"**
- `In Progress` → **"In Progress"**
- `Awaiting Review` → **"Awaiting Supervisor Review"**
- `Released` → **"Released"**
- `Closed` → **"Closed"**

### Admin Role
- All statuses display their system names (New, Returned, Assigned, In Progress, Awaiting Review, Released, Closed)

## Implementation Details

### Frontend Components Updated
- `JobList.tsx` - Job listing component with role-based status badges
- `jobs/[id]/page.tsx` - Job detail page with role-based status display
- `staff/page.tsx` - Staff dashboard with role-based status filtering
- `supervisor/page.tsx` - Supervisor dashboard with role-based status display

### Backend Updates
- `JobEnums.cs` - Updated enum values to new status names
- `Job.cs` - Updated default status to `New`
- `StaffingService.cs` - Updated all status transitions and validations

### Utility Functions
- `getRoleBasedStatusDisplay(status, userRole)` - Maps status to role-specific display text
- `getStatusColors(status)` - Returns color scheme for status badges
- `getStatusTailwindClasses(status)` - Returns Tailwind CSS classes for status badges

## Status Color Scheme
- **New**: Emerald (green) - Fresh submissions
- **Returned**: Yellow - Needs attention
- **Assigned**: Blue - In assignment phase
- **In-Progress**: Emerald (green) - Active work
- **Awaiting Review**: Yellow - Pending review
- **Released**: Purple - Delivered to client
- **Closed**: Green - Successfully completed

## Usage Examples

```typescript
import { getRoleBasedStatusDisplay, getStatusColors } from '@/utils/statusDisplay';

// Get role-specific display text
const staffView = getRoleBasedStatusDisplay('Assigned', 'Staff'); // Returns "Assigned to Me"
const clientView = getRoleBasedStatusDisplay('New', 'User'); // Returns "Submitted"
const supervisorView = getRoleBasedStatusDisplay('Awaiting Review', 'Supervisor'); // Returns "Awaiting My Review"

// Get status colors
const colors = getStatusColors('In Progress');
// Returns: { bg: '#f0fdf4', text: '#14532d', border: '#bbf7d0' }

// Get Tailwind classes
const classes = getStatusTailwindClasses('Awaiting Review');
// Returns: 'bg-yellow-100 text-yellow-800'
```

## Benefits

1. **Role-Specific Context**: Each user sees status names that make sense in their workflow context
2. **Improved UX**: Clients see "Submitted" instead of "New", Staff see "Assigned to Me" instead of "Assigned"
3. **Consistent Styling**: Unified color scheme across all roles while maintaining role-specific text
4. **Maintainable**: Centralized status display logic in utility functions
5. **Extensible**: Easy to add new roles or modify display mappings

## Testing

Unit tests are provided in `statusDisplay.test.ts` to verify:
- Correct role-based status mapping
- Color scheme consistency
- Handling of edge cases (unknown roles/statuses)
- Support for both normalized and hyphenated status names
