<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Test Users - Staff Hall</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background-color: #10b981;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #059669;
        }
        .button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #10b981;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .credentials {
            background-color: #e0f2fe;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #0277bd;
        }
        .warning {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Staff Hall Test Users Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> Make sure your Staff Hall API is running on <code>http://localhost:5000</code> before proceeding.
        </div>

        <p>This tool will create test users for all roles so you can test the role-based status display functionality.</p>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button" onclick="createAllUsers()" id="createBtn">
                Create All Test Users
            </button>
            <button class="button" onclick="clearResults()">
                Clear Results
            </button>
        </div>

        <div class="credentials">
            <h3>🔑 Test User Credentials</h3>
            <p>After creation, you can log in with these credentials:</p>
            <ul>
                <li><strong>Client (Individual):</strong> <EMAIL> / test123</li>
                <li><strong>Client (Corporate):</strong> <EMAIL> / test123</li>
                <li><strong>Staff:</strong> <EMAIL> / staff123</li>
                <li><strong>Supervisor:</strong> <EMAIL> / supervisor123</li>
                <li><strong>Admin:</strong> <EMAIL> / admin123</li>
            </ul>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';

        function log(message) {
            const results = document.getElementById('results');
            results.textContent += message + '\n';
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').textContent = '';
        }

        async function createAllUsers() {
            const btn = document.getElementById('createBtn');
            btn.disabled = true;
            btn.textContent = 'Creating Users...';
            
            clearResults();
            log('🚀 Starting test user creation...\n');

            try {
                // 1. Create Client User (Individual)
                log('📝 Creating Client User (Individual)...');
                try {
                    const clientResponse = await fetch(`${API_BASE}/auth/register`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            firstName: 'Test',
                            lastName: 'Client',
                            email: '<EMAIL>',
                            password: 'test123',
                            clientType: 'individual',
                            companyName: '',
                            role: 'User'
                        }),
                    });

                    if (clientResponse.ok) {
                        log('✅ Individual client user created successfully');
                    } else {
                        log('⚠️ Individual client user creation failed (may already exist)');
                    }
                } catch (error) {
                    log('❌ Error creating individual client: ' + error.message);
                }

                // 2. Create Corporate Client User
                log('\n📝 Creating Corporate Client User...');
                try {
                    const corporateResponse = await fetch(`${API_BASE}/auth/register`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            firstName: 'Corporate',
                            lastName: 'Client',
                            email: '<EMAIL>',
                            password: 'test123',
                            clientType: 'corporate',
                            companyName: 'Test Corporation',
                            role: 'User'
                        }),
                    });

                    if (corporateResponse.ok) {
                        log('✅ Corporate client user created successfully');
                    } else {
                        log('⚠️ Corporate client user creation failed (may already exist)');
                    }
                } catch (error) {
                    log('❌ Error creating corporate client: ' + error.message);
                }

                // 3. Seed all role users (includes Admin, Staff, Supervisor)
                log('\n📝 Creating Staff, Supervisor, and Admin users...');
                try {
                    const seedResponse = await fetch(`${API_BASE}/auth/seed-roles`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                    });

                    if (seedResponse.ok) {
                        log('✅ Staff, Supervisor, and Admin users created successfully');
                    } else {
                        log('⚠️ Role users creation failed (may already exist)');
                    }
                } catch (error) {
                    log('❌ Error creating role users: ' + error.message);
                }

                // 4. Verify users were created
                log('\n📋 Verifying user creation...');
                try {
                    const usersResponse = await fetch(`${API_BASE}/auth/test-users`);
                    if (usersResponse.ok) {
                        const users = await usersResponse.json();
                        log(`✅ Total users in system: ${users.length}`);
                        
                        const roleCount = users.reduce((acc, user) => {
                            acc[user.role] = (acc[user.role] || 0) + 1;
                            return acc;
                        }, {});
                        
                        log('📊 Users by role:');
                        Object.entries(roleCount).forEach(([role, count]) => {
                            log(`   ${role}: ${count} user(s)`);
                        });
                    }
                } catch (error) {
                    log('❌ Error verifying users: ' + error.message);
                }

                log('\n🎉 User creation process completed!');
                log('\n📋 TESTING INSTRUCTIONS:');
                log('1. Go to Staff Hall application (http://localhost:3000)');
                log('2. Log out if currently logged in');
                log('3. Log in with different role credentials (see above)');
                log('4. Check Work History section for role-based status display');
                log('5. Expected status mappings:');
                log('   • Client: New→Submitted, Released→Completed');
                log('   • Staff: Assigned→Assigned to Me');
                log('   • Supervisor: AwaitingReview→Awaiting My Review');

            } catch (error) {
                log('❌ Unexpected error: ' + error.message);
            } finally {
                btn.disabled = false;
                btn.textContent = 'Create All Test Users';
            }
        }

        // Auto-run on page load if desired
        // createAllUsers();
    </script>
</body>
</html>
