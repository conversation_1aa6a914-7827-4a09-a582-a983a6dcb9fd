using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using StaffingApp.Api.Data;
using StaffingApp.Api.Models;
using StaffingApp.Api.Models.DTOs;

namespace StaffingApp.Api.Services;

public class AuthService
{
    private readonly StaffingContext _context;
    private readonly IConfiguration _configuration;

    public AuthService(StaffingContext context, IConfiguration configuration)
    {
        _context = context;
        _configuration = configuration;
    }

    public async Task<LoginResponseDTO?> LoginAsync(LoginDTO loginDto)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == loginDto.Email);

        if (user == null || !PasswordHasher.VerifyPassword(loginDto.Password, user.Password, user.PasswordSalt))
        {
            return null;
        }

        var token = GenerateJwtToken(user);
        return new LoginResponseDTO
        {
            Token = token,
            User = new UserDTO
            {
                Id = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                ClientId = user.ClientId,
                Role = user.Role
            }
        };
    }

    public async Task<LoginResponseDTO?> RegisterAsync(RegisterDTO registerDto)
    {
        // Check if user already exists
        var existingUser = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == registerDto.Email);

        if (existingUser != null)
        {
            return null; // User already exists
        }

        // Create a unique client for each user (both individual and corporate)
        Client client;
        if (registerDto.ClientType.ToLower() == "corporate")
        {
            // For corporate accounts, create a new client with the company name
            client = new Client
            {
                Name = registerDto.CompanyName,
                ClientType = ClientType.Corporate
            };
        }
        else
        {
            // For individual accounts, create a unique client for each user
            client = new Client
            {
                Name = $"{registerDto.FirstName} {registerDto.LastName}",
                ClientType = ClientType.Individual
            };
        }

        _context.Clients.Add(client);
        await _context.SaveChangesAsync(); // Save to get the client ID

        // Hash the password
        var hashedPassword = PasswordHasher.HashPassword(registerDto.Password, out string salt);

        // Create new user
        var user = new User
        {
            FirstName = registerDto.FirstName,
            LastName = registerDto.LastName,
            Email = registerDto.Email,
            Password = hashedPassword,
            PasswordSalt = salt,
            ClientId = client.Id,
            Role = registerDto.Role
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Generate token and return response
        var token = GenerateJwtToken(user);
        return new LoginResponseDTO
        {
            Token = token,
            User = new UserDTO
            {
                Id = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                ClientId = user.ClientId,
                Role = user.Role
            }
        };
    }

    private string GenerateJwtToken(User user)
{
    var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JwtSettings:SecretKey"]!));
    var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

    var claims = new[]
    {
        new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
        new Claim(ClaimTypes.Email, user.Email),
        new Claim(ClaimTypes.GivenName, user.FirstName),
        new Claim(ClaimTypes.Surname, user.LastName),
        new Claim(ClaimTypes.Role, user.Role),
        new Claim("ClientId", user.ClientId.ToString())
    };

    var token = new JwtSecurityToken(
            issuer: _configuration["JwtSettings:Issuer"],
            audience: _configuration["JwtSettings:Audience"],
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(Convert.ToDouble(_configuration["JwtSettings:ExpirationInMinutes"])),
            signingCredentials: credentials
        );

    return new JwtSecurityTokenHandler().WriteToken(token);
}

    public async Task<object> GetAllUsersAsync()
    {
        var users = await _context.Users.Select(u => new
        {
            u.Id,
            u.Email,
            u.FirstName,
            u.LastName,
            u.ClientId,
            HasPassword = !string.IsNullOrEmpty(u.Password),
            HasSalt = !string.IsNullOrEmpty(u.PasswordSalt)
        }).ToListAsync();

        return users;
    }

    public async Task<object> GetStaffUsersAsync()
    {
        var staffUsers = await _context.Users
            .Where(u => u.Role == "Staff")
            .Select(u => new
            {
                u.Id,
                u.Email,
                u.FirstName,
                u.LastName,
                u.ClientId,
                Role = u.Role
            }).ToListAsync();

        return staffUsers;
    }

    public async Task SeedRoleUsersAsync()
    {
        // Check if supervisor user already exists
        var existingSupervisor = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == "<EMAIL>");

        if (existingSupervisor == null)
        {
            // Get a corporate client to assign users to
            var corporateClient = await _context.Clients
                .FirstOrDefaultAsync(c => c.ClientType == ClientType.Corporate);

            if (corporateClient == null)
            {
                // Create a corporate client for internal users
                corporateClient = new Client
                {
                    Name = "Staff Hall Internal",
                    ClientType = ClientType.Corporate
                };
                _context.Clients.Add(corporateClient);
                await _context.SaveChangesAsync();
            }

            // Add supervisor user
            var supervisorPassword = "supervisor123";
            var supervisorUser = new User
            {
                FirstName = "Sarah",
                LastName = "Supervisor",
                Email = "<EMAIL>",
                Password = PasswordHasher.HashPassword(supervisorPassword, out string supervisorSalt),
                PasswordSalt = supervisorSalt,
                ClientId = corporateClient.Id,
                Role = "Supervisor"
            };
            _context.Users.Add(supervisorUser);
        }

        // Check if staff user already exists
        var existingStaff = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == "<EMAIL>");

        if (existingStaff == null)
        {
            // Get a corporate client to assign users to
            var corporateClient = await _context.Clients
                .FirstOrDefaultAsync(c => c.ClientType == ClientType.Corporate);

            if (corporateClient == null)
            {
                // Create a corporate client for internal users
                corporateClient = new Client
                {
                    Name = "Staff Hall Internal",
                    ClientType = ClientType.Corporate
                };
                _context.Clients.Add(corporateClient);
                await _context.SaveChangesAsync();
            }

            // Add staff user
            var staffPassword = "staff123";
            var staffUser = new User
            {
                FirstName = "Mike",
                LastName = "Staff",
                Email = "<EMAIL>",
                Password = PasswordHasher.HashPassword(staffPassword, out string staffSalt),
                PasswordSalt = staffSalt,
                ClientId = corporateClient.Id,
                Role = "Staff"
            };
            _context.Users.Add(staffUser);
        }

        await _context.SaveChangesAsync();
    }

    public async Task<object> GetAllUsersWithRolesAsync()
    {
        var users = await _context.Users.Select(u => new
        {
            u.Id,
            u.Email,
            u.FirstName,
            u.LastName,
            u.ClientId,
            Role = u.Role,
            HasPassword = !string.IsNullOrEmpty(u.Password),
            HasSalt = !string.IsNullOrEmpty(u.PasswordSalt)
        }).ToListAsync();

        return users;
    }

    public async Task<object> CreateSupervisorUserAsync()
    {
        // First, delete existing supervisor if exists
        var existingSupervisor = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == "<EMAIL>");

        if (existingSupervisor != null)
        {
            _context.Users.Remove(existingSupervisor);
            await _context.SaveChangesAsync();
        }

        // Get or create a corporate client
        var corporateClient = await _context.Clients
            .FirstOrDefaultAsync(c => c.ClientType == ClientType.Corporate);

        if (corporateClient == null)
        {
            corporateClient = new Client
            {
                Name = "Staff Hall Internal",
                ClientType = ClientType.Corporate
            };
            _context.Clients.Add(corporateClient);
            await _context.SaveChangesAsync();
        }

        // Create supervisor user
        var supervisorPassword = "supervisor123";
        var supervisorUser = new User
        {
            FirstName = "Sarah",
            LastName = "Supervisor",
            Email = "<EMAIL>",
            Password = PasswordHasher.HashPassword(supervisorPassword, out string supervisorSalt),
            PasswordSalt = supervisorSalt,
            ClientId = corporateClient.Id,
            Role = "Supervisor"
        };

        _context.Users.Add(supervisorUser);
        await _context.SaveChangesAsync();

        return new
        {
            message = "Supervisor user created successfully",
            user = new
            {
                supervisorUser.Id,
                supervisorUser.Email,
                supervisorUser.FirstName,
                supervisorUser.LastName,
                supervisorUser.Role,
                supervisorUser.ClientId,
                HasPassword = !string.IsNullOrEmpty(supervisorUser.Password),
                HasSalt = !string.IsNullOrEmpty(supervisorUser.PasswordSalt)
            }
        };
    }

    public async Task<object> CreateStaffUserAsync()
    {
        // First, delete existing staff if exists
        var existingStaff = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == "<EMAIL>");

        if (existingStaff != null)
        {
            _context.Users.Remove(existingStaff);
            await _context.SaveChangesAsync();
        }

        // Get or create a corporate client
        var corporateClient = await _context.Clients
            .FirstOrDefaultAsync(c => c.ClientType == ClientType.Corporate);

        if (corporateClient == null)
        {
            corporateClient = new Client
            {
                Name = "Staff Hall Internal",
                ClientType = ClientType.Corporate
            };
            _context.Clients.Add(corporateClient);
            await _context.SaveChangesAsync();
        }

        // Create staff user
        var staffPassword = "staff123";
        var staffUser = new User
        {
            FirstName = "Mike",
            LastName = "Staff",
            Email = "<EMAIL>",
            Password = PasswordHasher.HashPassword(staffPassword, out string staffSalt),
            PasswordSalt = staffSalt,
            ClientId = corporateClient.Id,
            Role = "Staff"
        };

        _context.Users.Add(staffUser);
        await _context.SaveChangesAsync();

        return new
        {
            message = "Staff user created successfully",
            user = new
            {
                staffUser.Id,
                staffUser.Email,
                staffUser.FirstName,
                staffUser.LastName,
                staffUser.Role,
                staffUser.ClientId,
                HasPassword = !string.IsNullOrEmpty(staffUser.Password),
                HasSalt = !string.IsNullOrEmpty(staffUser.PasswordSalt)
            }
        };
    }
}
